package com.iciyun.aiguide.controller.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iciyun.common.annotation.Anonymous;
import com.iciyun.common.constant.CacheConstants;
import com.iciyun.common.core.controller.BaseController;
import com.iciyun.common.core.domain.ClearCacheDto;
import com.iciyun.common.core.domain.LabelTextDto;
import com.iciyun.common.core.domain.R;
import com.iciyun.common.core.domain.entity.SysDictData;
import com.iciyun.common.core.domain.model.KeyValDto;
import com.iciyun.common.core.domain.model.UserStyleDto;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.utils.GuavaCacheUtils;
import com.iciyun.common.utils.HelpMe;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.domain.dto.*;
import com.iciyun.system.mapper.ScenicSpotCustomizeMapper;
import com.iciyun.system.mapper.SysTouristLabelMapper;
import com.iciyun.system.mapper.SysUserMapper;
import com.iciyun.system.service.IScenicSpotCustomizeService;
import com.iciyun.system.service.ISysDictDataService;
import com.iciyun.system.service.ISysUserService;
import com.iciyun.system.service.ScenicCacheManager;
import com.iciyun.task.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 景区缓存
 *
 * <AUTHOR> on 2025-05-26 11:12.
 */
@Slf4j
@Anonymous
@RestController
@RequestMapping("/guide/cache")
public class ScenicCacheCtrl extends BaseController {

    private final ExecutorService executorService = Executors.newFixedThreadPool(100);

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private ISysUserService userService;
    @Autowired
    ScenicCacheManager scenicCacheManager;
    @Autowired
    ScenicSpotCustomizeMapper scenicSpotCustomizeMapper;
    @Autowired
    IScenicSpotCustomizeService scenicSpotCustomizeService;
    @Autowired
    SysTouristLabelMapper sysTouristLabelMapper;
    @Autowired
    ScenicCacheService scenicCacheService;
    @Autowired
    LabelTextCacheTask labelTextCacheTask;
    @Autowired
    LabelAudioCacheTask labelAudioCacheTask;
    @Autowired
    TouristDetailAudioCacheTask touristDetailAudioCacheTask;
    @Autowired
    TouristDetailTextCacheTask touristDetailTextCacheTask;
    @Autowired
    GenTextAgainService genTextAgainService;
    @Autowired
    GenAudioBySexTask genAudioBySexTask;
    @Autowired
    DealOtherLanguageTask dealOtherLanguageTask;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    ISysDictDataService sysDictDataService;
    @Autowired
    ThreadPoolTaskExecutor threadPoolTaskExecutor;




    /**
     * 删除特定标签的缓存
     */
    @PostMapping(value = "/clearCache")
    public R clearCache(@RequestBody ClearCacheDto clearCacheDto) {
        String voiceId = userService.getVoiceId(clearCacheDto.getUserStyleDto());

        UserStyleDto userStyleDto = clearCacheDto.getUserStyleDto();

        String cacheKey = scenicCacheManager.getCacheKey(clearCacheDto.getUserStyleDto(),
                clearCacheDto.getScenicId(), clearCacheDto.getScenicLabel(), voiceId);

        Map<String, Object> result = Maps.newHashMap();

        if (redisCache.hasKey(cacheKey)) {
            redisCache.deleteObject(cacheKey);
            log.info("已删除缓存，key={}", cacheKey);
            result.put("msg", "已删除缓存, key=" + cacheKey);

            String labelName = clearCacheDto.getScenicLabel();
            if (StrUtil.isNotEmpty(labelName)) {
                SysTouristLabel sysTouristLabel = new SysTouristLabel();
                sysTouristLabel.setTouristId(Long.parseLong(clearCacheDto.getScenicId()));
                sysTouristLabel.setLabelName(clearCacheDto.getScenicLabel());
                List<SysTouristLabel> tempList = sysTouristLabelMapper.selectSysTouristLabelList(sysTouristLabel);
                for (SysTouristLabel item : tempList) {
                    resetSysTouristLabel(userStyleDto, item);
                    sysTouristLabelMapper.resetCache(item);
                }
            } else {
                ScenicSpot scenicSpot = scenicSpotCustomizeMapper.selectById(Integer.parseInt(clearCacheDto.getScenicId()));
                resetScenicSpot(userStyleDto, scenicSpot);
                scenicSpotCustomizeMapper.resetCache(scenicSpot);
            }
        } else {
            log.info("缓存不存在，key={}", cacheKey);
            result.put("msg", "缓存不存在, key=" + cacheKey);
        }

        return R.ok(result);
    }


    private void resetScenicSpot(UserStyleDto userStyleDto, ScenicSpot scenicSpot) {
        JSONArray arr = null;
        String oldLabelText = scenicSpot.getLabelText();
        try {
            arr = JSONUtil.parseArray(oldLabelText);
        } catch (Exception e) {
        }

        List<LabelTextDto> dtoList = Lists.newArrayList();
        for (Object obj : arr) {
            JSONObject jsonObject = JSONUtil.parseObj(obj);
            LabelTextDto dto = JSONUtil.toBean(jsonObject, LabelTextDto.class);
            if (userStyleDto.getGuideStyle().equals(dto.getStyle()) && userStyleDto.getLanguageFlag().equals(dto.getLanguage())) {
                //需要删除
            } else {
                dtoList.add(dto);
            }
        }

        scenicSpot.setCacheOk(0);
        scenicSpot.setAudioCacheOk(0);
        scenicSpot.setLabelText(JSONUtil.toJsonStr(dtoList));
    }

    private void resetSysTouristLabel(UserStyleDto userStyleDto, SysTouristLabel sysTouristLabel) {
        JSONArray arr = null;
        String oldLabelText = sysTouristLabel.getLabelText();
        try {
            arr = JSONUtil.parseArray(oldLabelText);
        } catch (Exception e) {
        }

        List<LabelTextDto> dtoList = Lists.newArrayList();
        for (Object obj : arr) {
            JSONObject jsonObject = JSONUtil.parseObj(obj);
            LabelTextDto dto = JSONUtil.toBean(jsonObject, LabelTextDto.class);
            if (userStyleDto.getGuideStyle().equals(dto.getStyle()) && userStyleDto.getLanguageFlag().equals(dto.getLanguage())) {
                //需要删除
            } else {
                dtoList.add(dto);
            }
        }

        sysTouristLabel.setCacheOk(0);
        sysTouristLabel.setAudioCacheOk(0);
        sysTouristLabel.setLabelText(JSONUtil.toJsonStr(dtoList));
    }


    private void dealKeyValDtoList(List<KeyValDto> keyValDtoList) {
        String url = "https://ai.ideepyou.com/prod-api/guide/cache/set2Redis_Batch";
        HttpUtil.post(url, JSONUtil.toJsonStr(keyValDtoList));
    }

    /**
     * 删除景区缓存--全部
     */
    @GetMapping(value = "/delCache")
    public R delCache(Long scenicId) {

        ScenicSpot scenicSpot = scenicSpotCustomizeService.getById(Integer.parseInt(scenicId + ""));

        Collection<String> keys = redisCache.keys(CacheConstants.scenicPixKey + scenicId + "_*");

        for (String key : keys) {
            redisCache.deleteObject(key);
        }

        String result = "已清空 [" + scenicSpot.getName() + "] 的 [" + keys.size() + "] 条缓存信息";

        SysTouristLabel sysTouristLabel = new SysTouristLabel();
        sysTouristLabel.setTouristId(scenicId);
        List<SysTouristLabel> tempList = sysTouristLabelMapper.selectSysTouristLabelList(sysTouristLabel);
        for (SysTouristLabel item : tempList) {
            item.setCacheOk(0);
            item.setAudioCacheOk(0);
            item.setLabelText("[]");
            sysTouristLabelMapper.resetCache(item);
        }

        scenicSpot.setCacheOk(0);
        scenicSpot.setAudioCacheOk(0);
        scenicSpot.setLabelText("[]");
        scenicSpotCustomizeMapper.resetCache(scenicSpot);

        return R.ok((Object) result);
    }


    /**
     * 清空临时队列 --> 生成 景区简介 解说词
     */
    @GetMapping(value = "/genDetailText_Clear")
    public R genDetailText_Clear() {
        touristDetailTextCacheTask.clear();

        return R.ok();
    }

    /**
     * 清空临时队列 --> PC后台手动操作生成音频
     */
    @GetMapping(value = "/genAudioBySexTask_Clear")
    public R genAudioBySexTask_Clear() {
        genAudioBySexTask.clear();

        return R.ok();
    }

    /**
     * 清空临时队列 --> 外文文本及音频生成
     */
    @GetMapping(value = "/dealOtherLanguageTask_Clear")
    public R dealOtherLanguageTask_Clear() {
        dealOtherLanguageTask.clear();

        return R.ok();
    }

    /**
     * 当前队列任务数 --> PC后台手动操作生成音频
     */
    @GetMapping(value = "/genAudioBySexTask_Size")
    public R genAudioBySexTask_Size() {

        Object size = genAudioBySexTask.size();

        return R.ok(size);
    }

    /**
     * 当前队列任务数 --> 外文文本及音频生成
     */
    @GetMapping(value = "/dealOtherLanguageTask_Size")
    public R dealOtherLanguageTask_Size() {

        Object size = dealOtherLanguageTask.size();

        return R.ok(size);
    }


    /**
     * 生成 景区简介 解说词
     */
    @GetMapping(value = "/genDetailText")
    public R genDetailText(Long scenicId) {
        //通过景区ID,获取景区信息
        ScenicSpot scenicSpot = scenicSpotCustomizeService.getById(scenicId);

        String result = "";
        if (scenicSpot.getCacheOk() == 0) {
            touristDetailTextCacheTask.enqueue(scenicSpot);
            result = "后台生成 景区简介 解说词，景区名称：[" + scenicSpot.getName() + "]";
        } else {
            result = "景区简介 解说词 已生成，景区名称：[" + scenicSpot.getName() + "]";
        }

        return R.ok((Object) result);
    }


    /**
     * 清空临时队列 --> 生成 景区简介 音频
     */
    @GetMapping(value = "/genDetailAudio_Clear")
    public R genDetailAudio_Clear() {
        touristDetailAudioCacheTask.clear();

        return R.ok();
    }

    /**
     * 生成 景区简介 音频
     */
    @GetMapping(value = "/genDetailAudio")
    public R genDetailAudio(Long scenicId) {
        //通过景区ID,获取景区信息
        ScenicSpot scenicSpot = scenicSpotCustomizeService.getById(scenicId);

        touristDetailAudioCacheTask.enqueue(scenicSpot);

        String result = "后台生成 景区简介 音频，景区名称：[" + scenicSpot.getName() + "]";

        return R.ok((Object) result);
    }


    /**
     * 清空临时队列 --> 生成 label 解说词
     */
    @GetMapping(value = "/genLabelText_Clear")
    public R genLabelText_Clear() {
        labelTextCacheTask.clear();
        return R.ok();
    }


    /**
     * 生成 label 解说词
     */
    @PostMapping(value = "/genLabelText")
    public R genLabelText(@RequestBody CacheParamDto cacheParamDto) {
        List<SysTouristLabel> labelList = Lists.newArrayList();

        if (cacheParamDto.getScenicId() != null && cacheParamDto.getScenicId() > 0) {

            SysTouristLabel sysTouristLabel = new SysTouristLabel();
            sysTouristLabel.setTouristId(cacheParamDto.getScenicId());
            sysTouristLabel.setCacheOk(0);

            labelList = sysTouristLabelMapper.selectSysTouristLabelList(sysTouristLabel);
        }
        if (StrUtil.isNotEmpty(cacheParamDto.getDetailAddress())) {
            ScenicSpot scenicSpot = new ScenicSpot();
            scenicSpot.setDetailAddress(cacheParamDto.getDetailAddress());

            List<Integer> idList = scenicSpotCustomizeMapper.selectIdList(scenicSpot);

            List<Long> tempIdList = idList.stream().map(item -> {
                return Long.parseLong(item + "");
            }).collect(Collectors.toList());

            SysTouristLabelQueryParam param = new SysTouristLabelQueryParam();
            param.setCacheOk(0);
            param.setTouristIdList(tempIdList);

            List<SysTouristLabel> tempList = sysTouristLabelMapper.selectListByParam(param);

            labelList.addAll(tempList);
        }

        for (SysTouristLabel item : labelList) {
            labelTextCacheTask.enqueue(item);
        }

        String result = "后台生成 label 文本，待生成 label 个数：[" + labelList.size() + "]";

        return R.ok((Object) result);
    }


    /**
     * 清空临时队列 --> 生成 label 音频
     */
    @GetMapping(value = "/genLabelAudio_Clear")
    public R genLabelAudio_Clear() {
        labelAudioCacheTask.clear();
        return R.ok();
    }

    /**
     * 生成 label 音频
     */
    @PostMapping(value = "/genLabelAudio")
    public R genLabelAudio(@RequestBody CacheParamDto cacheParamDto) {

        List<SysTouristLabel> labelList = Lists.newArrayList();

        if (cacheParamDto.getScenicId() != null && cacheParamDto.getScenicId() > 0) {

            SysTouristLabel sysTouristLabel = new SysTouristLabel();
            sysTouristLabel.setTouristId(cacheParamDto.getScenicId());
            sysTouristLabel.setCacheOk(1);
            sysTouristLabel.setAudioCacheOk(0);

            labelList = sysTouristLabelMapper.selectSysTouristLabelList(sysTouristLabel);
        }
        if (StrUtil.isNotEmpty(cacheParamDto.getDetailAddress())) {
            ScenicSpot scenicSpot = new ScenicSpot();
            scenicSpot.setDetailAddress(cacheParamDto.getDetailAddress());

            List<Integer> idList = scenicSpotCustomizeMapper.selectIdList(scenicSpot);

            List<Long> tempIdList = idList.stream().map(item -> {
                return Long.parseLong(item + "");
            }).collect(Collectors.toList());

            SysTouristLabelQueryParam param = new SysTouristLabelQueryParam();
            param.setCacheOk(1);
            param.setAudioCacheOk(0);
            param.setTouristIdList(tempIdList);

            List<SysTouristLabel> tempList = sysTouristLabelMapper.selectListByParam(param);

            labelList.addAll(tempList);
        }

        for (SysTouristLabel item : labelList) {
            labelAudioCacheTask.enqueue(item);
        }

        String result = "后台生成 label 音频，待生成 label 个数：[" + labelList.size() + "]";

        return R.ok((Object) result);
    }


    /**
     * 查询 label 缓存信息
     */
    @GetMapping(value = "/labelCacheInfo")
    public R labelCacheInfo() {

        List<LabelCacheInfoDto> labelList = sysTouristLabelMapper.labelCacheInfo();

        Map<String, Object> result = Maps.newLinkedHashMap();

        for (LabelCacheInfoDto item : labelList) {
            if (item.getSource().equals("total")) {
                result.put("总 label", item.getCnt());
            } else if (item.getSource().equals("cacheOk")) {
                result.put("已生成解说词 label", item.getCnt());
            } else if (item.getSource().equals("audioCacheOk")) {
                result.put("已生成音频 label", item.getCnt());
            }
        }

        return R.ok(result);
    }


    /**
     * 查询 label 缓存信息 -- 特定景区
     */
    @GetMapping(value = "/labelCacheInfoByTourist")
    public R labelCacheInfoByTourist(Long scenicId) {

        ScenicSpot scenicSpot = scenicSpotCustomizeService.getById(scenicId);

        SysTouristLabel sysTouristLabel = new SysTouristLabel();
        sysTouristLabel.setTouristId(scenicId);
        List<SysTouristLabel> labelList = sysTouristLabelMapper.selectSysTouristLabelList(sysTouristLabel);

        int cacheOk = labelList.stream().filter(item -> {
            return item.getCacheOk() != null && item.getCacheOk() == 1;
        }).collect(Collectors.toList()).size();

        int audioCacheOk = labelList.stream().filter(item -> {
            return item.getCacheOk() != null && item.getCacheOk() == 1 && item.getAudioCacheOk() != null && item.getAudioCacheOk() == 1;
        }).collect(Collectors.toList()).size();

        Map<String, Object> result = Maps.newLinkedHashMap();
        result.put("景区名称", scenicSpot.getName());
        Integer flag1 = scenicSpot.getCacheOk();
        Integer flag2 = scenicSpot.getAudioCacheOk();
        result.put("简介解说词生成", flag1 == 1);
        result.put("简介音频生成", flag2 == 1);

        result.put("总 label", labelList.size());
        result.put("已生成解说词 label", cacheOk);
        result.put("已生成音频 label", audioCacheOk);

        return R.ok(result);
    }


    /**
     * 查询 label 列表
     */
    @GetMapping(value = "/labelListByScenicIdAndLabelName")
    public R labelListByScenicIdAndLabelName(SysTouristLabelDto sysTouristLabelDto) {
        SysTouristLabel sysTouristLabel = new SysTouristLabel();
        sysTouristLabel.setTouristId(sysTouristLabelDto.getTouristId());
        sysTouristLabel.setLabelName(sysTouristLabelDto.getLabelName());

        List<SysTouristLabel> labelList = sysTouristLabelMapper.selectSysTouristLabelList_Simple(sysTouristLabel);

        SysTouristLabel one = new SysTouristLabel();
        one.setId(0L);
        one.setLabelName("景区简介");
        one.setNote("");
        one.setTouristId(sysTouristLabelDto.getTouristId());

        //添加景区简介
        labelList.add(0, one);

        return R.ok(labelList);
    }


    /**
     * 查询 label 详情
     */
    @GetMapping(value = "/getOneLabelInfo")
    public R getOneLabelInfo(Long touristId, Long labelId) {
        String labelText = "";

        if (labelId == 0) {
            ScenicSpot scenicSpot = scenicSpotCustomizeService.getById(touristId.intValue());
            labelText = scenicSpot.getLabelText();
        } else {
            SysTouristLabel sysTouristLabel = sysTouristLabelMapper.selectSysTouristLabelById(labelId);
            labelText = sysTouristLabel.getLabelText();
        }

        Map<String, Object> result = Maps.newLinkedHashMap();
        result.put("labelText", labelText);
        result.put("touristId", touristId);
        result.put("labelId", labelId);

        return R.ok(result);
    }

    /**
     * 更新 label 备注
     *
     * @return
     */
    @PostMapping(value = "/updateLabelNote")
    @Anonymous
    public R updateLabelNote(@RequestBody UpdateNoteParam updateNoteParam) {

        SysTouristLabel sysTouristLabel = sysTouristLabelMapper.selectSysTouristLabelById(updateNoteParam.getLabelId());
        sysTouristLabel.setNote(updateNoteParam.getNote());

        sysTouristLabelMapper.updateNote(sysTouristLabel);

        return R.ok();
    }


    GuavaCacheUtils<String, String> cacheUtils = new GuavaCacheUtils<>(5, 20, TimeUnit.MINUTES);

    /**
     * 重新生成解说词
     *
     * @return
     */
    @PostMapping(value = "/genTextAgain")
    public R genTextAgain(@RequestBody GenTextAgainParam genTextAgainParam) {
        log.info("重新生成解说词，参数：{}", genTextAgainParam);

        if (genTextAgainParam.getType() == 0) {
            String key = genTextAgainParam.getScenicId() + "_" + genTextAgainParam.getLabelId();
            if (cacheUtils.getIfPresent(key) != null) {
                //暂存 5 分钟，避免页面重复操作多次触发
                return R.ok("正在处理中，请稍后再试");
            } else {
                cacheUtils.putCache(key, "1");
            }
        }
        if (genTextAgainParam.getType() == -1) {
            String key = genTextAgainParam.getScenicId() + "";
            if (cacheUtils.getIfPresent(key) != null) {
                //暂存 5 分钟，避免页面重复操作多次触发
                return R.ok("正在处理中，请稍后再试");
            } else {
                cacheUtils.putCache(key, "1");
            }
        }

        ScenicSpot scenicSpot = scenicSpotCustomizeService.getById(genTextAgainParam.getScenicId().intValue());

        String labelText = "";

        //-1 重新生成（整个景区的全部）  0 重新生成（某个景点的全部）   1 重新生成（针对某个风格）     2 再次保存（针对某个风格）
        if (genTextAgainParam.getType()==-1){
            /**
             * {
             *     "labelId": 0,
             *     "scenicId": "4998",
             *     "type": 0
             * }
             */
            GenTextAgainParam param = new GenTextAgainParam();
            param.setType(0);
            param.setScenicId(genTextAgainParam.getScenicId());
            param.setLabelId(0L);
            genTextAgainService.dealGenTextAgain(scenicSpot, param);

            /**
             * {
             *     "labelId": 84263,
             *     "scenicId": "4998",
             *     "type": 0
             * }
             */
            SysTouristLabelQueryParam sysTouristLabelQueryParam = new SysTouristLabelQueryParam();
            sysTouristLabelQueryParam.setTouristIdList(Lists.newArrayList(genTextAgainParam.getScenicId()));
            List<SysTouristLabel> list = sysTouristLabelMapper.selectListByParam(sysTouristLabelQueryParam);
            for (SysTouristLabel sysTouristLabel : list) {
                GenTextAgainParam param2 = new GenTextAgainParam();
                param2.setType(0);
                param2.setScenicId(genTextAgainParam.getScenicId());
                param2.setLabelId(sysTouristLabel.getId());
                genTextAgainService.dealGenTextAgain(scenicSpot, sysTouristLabel, param2);
            }
        }else {
            if (genTextAgainParam.getLabelId() == 0) {
                //处理景区简介
                labelText = genTextAgainService.dealGenTextAgain(scenicSpot, genTextAgainParam);
            } else {
                //处理景区 label
                SysTouristLabel sysTouristLabel = sysTouristLabelMapper.selectSysTouristLabelById(genTextAgainParam.getLabelId());
                labelText = genTextAgainService.dealGenTextAgain(scenicSpot, sysTouristLabel, genTextAgainParam);
            }
        }


        Map<String, Object> result = Maps.newLinkedHashMap();
        result.put("labelText", labelText);

        return R.ok(result);
    }


    /**
     * 试听音频
     *
     * @param playAudioParam
     * @return
     */
    @PostMapping(value = "/playAudio")
    public R playAudio(@RequestBody PlayAudioParam playAudioParam) {

        UserStyleDto userStyleDto = new UserStyleDto();
        userStyleDto.setLanguageFlag(playAudioParam.getLanguage());
        userStyleDto.setGuideStyle(playAudioParam.getStyle());
        userStyleDto.setSexType(playAudioParam.getSex());

        String labelName = playAudioParam.getLabelName();
        if (labelName.equals("景区简介")) {
            labelName = "";
        }

        String voiceId = userService.getVoiceId(userStyleDto);

        String labelKay = scenicCacheManager.getCacheKey(userStyleDto, playAudioParam.getScenicId() + "", labelName, voiceId);

        String url = redisCache.getCacheObject(labelKay);

        Map<String, Object> result = Maps.newLinkedHashMap();
        result.put("url", url);

        return R.ok(result);
    }


    /**
     * 生成非中文的解说词及音频
     * @param genOtherLanguageAudioParam
     * @return
     */
    @PostMapping(value = "/genOtherLanguageAudio")
    public R genOtherLanguageAudio(@RequestBody GenOtherLanguageAudioParam genOtherLanguageAudioParam) {

        genTextAgainService.genOtherLanguageAudio(genOtherLanguageAudioParam);

        return R.ok(genOtherLanguageAudioParam);
    }

    /**
     * 重新生成音频
     * @param genAudioAgainParam
     * @return
     */
    @PostMapping(value = "/genAudioAgain")
    public R genAudioAgain(@RequestBody GenAudioAgainParam genAudioAgainParam) {

        genTextAgainService.genAudioAgain(genAudioAgainParam);

        return R.ok(genAudioAgainParam);
    }




    /**
     * 复制默认风格 [文化内含] 的解说词
     *
     * @param copyLabelTextParam
     * @return
     */
    @PostMapping(value = "/copyLabelText")
    public R copyLabelText(@RequestBody CopyLabelTextParam copyLabelTextParam) {
        ScenicSpot scenicSpot = scenicSpotCustomizeService.getById(copyLabelTextParam.getScenicId());

        //label ID：为 0 时表示景区简介，为 null 表示所有label，大于 0 表示某个label
        Long labelId = copyLabelTextParam.getLabelId();
        if (labelId == null) {
            threadPoolTaskExecutor.execute(()->{
                //处理简介
                CopyLabelTextParam textParam = new CopyLabelTextParam();
                BeanUtil.copyProperties(copyLabelTextParam, textParam, HelpMe.copyOptions());
                textParam.setLabelId(0L);
                copyLabelText(textParam,scenicSpot);

                SysTouristLabelQueryParam param = new SysTouristLabelQueryParam();
                param.setTouristIdList(Lists.newArrayList(Long.parseLong(copyLabelTextParam.getScenicId()+"")));
                List<SysTouristLabel> labelList = sysTouristLabelMapper.selectListByParam(param);
                for (SysTouristLabel sysTouristLabel : labelList) {
                    //处理某个label
                    CopyLabelTextParam tempParam = new CopyLabelTextParam();
                    BeanUtil.copyProperties(copyLabelTextParam, tempParam, HelpMe.copyOptions());
                    tempParam.setLabelId(sysTouristLabel.getId());

                    copyLabelText(tempParam,scenicSpot);

                    ThreadUtil.safeSleep(1000);
                }
            });
        }else {
            threadPoolTaskExecutor.execute(()->{
                copyLabelText(copyLabelTextParam,scenicSpot);
            });
        }

        return R.ok(copyLabelTextParam);
    }




    public void copyLabelText(CopyLabelTextParam copyLabelTextParam,ScenicSpot scenicSpot) {

        ScenicSpot.StyleAndLanguage styleAndLanguage = scenicSpot.parseStyleAndLanguage();
        List<String> styleList = styleAndLanguage.getStyle();

        styleList = styleList.stream().filter(item->{
            return !item.equals("CULTURE");
        }).collect(Collectors.toList());


        String labelText = "";
        SysTouristLabel sysTouristLabel = null;

        if (copyLabelTextParam.getLabelId() == 0) {
            labelText = scenicSpot.getLabelText();
        } else {
            sysTouristLabel = sysTouristLabelMapper.selectSysTouristLabelById(copyLabelTextParam.getLabelId());
            labelText = sysTouristLabel.getLabelText();
        }

        List<LabelTextDto> dtoList = parseLabelText(labelText);
        LabelTextDto defaultDto = defaultLabelTextDto(dtoList);
        List<LabelTextDto> otherLanguageLabelTextDtoList = otherLanguageLabelTextDto(dtoList);

        if (defaultDto == null) {
            return;
        }
        List<LabelTextDto> list = Lists.newArrayList();
        list.add(defaultDto);
        list.addAll(otherLanguageLabelTextDtoList);


        for (String style : styleList) {
            LabelTextDto labelTextDto = new LabelTextDto();
            BeanUtil.copyProperties(defaultDto, labelTextDto, HelpMe.copyOptions());
            labelTextDto.setUpdateTimeStr(DateUtil.now());
            labelTextDto.setLabelText(defaultDto.getLabelText());
            labelTextDto.setStyle(style);
            list.add(labelTextDto);

            //添加到音频生成队列
            genAudioBySexTask.enqueue(labelTextDto);
        }

        if (copyLabelTextParam.getLabelId() == 0) {
            scenicSpot.setLabelText(JSONUtil.toJsonStr(list));
            scenicSpotCustomizeService.updateById(scenicSpot);
        }else {
            sysTouristLabel.setLabelText(JSONUtil.toJsonStr(list));
            sysTouristLabelMapper.updateSysTouristLabel(sysTouristLabel);
        }

    }


    private List<LabelTextDto> parseLabelText(String labelText) {
        JSONArray arr = null;
        try {
            arr = JSONUtil.parseArray(labelText);
        } catch (Exception e) {
        }
        List<LabelTextDto> dtoList = Lists.newArrayList();
        if (arr == null) return dtoList;
        for (Object obj : arr) {
            JSONObject jsonObject = JSONUtil.parseObj(obj);
            LabelTextDto dto = JSONUtil.toBean(jsonObject, LabelTextDto.class);
            dtoList.add(dto);
        }
        return dtoList;
    }


    private LabelTextDto defaultLabelTextDto(List<LabelTextDto> list) {
        for (LabelTextDto dto : list) {
            if (dto.getLanguage().equals("Chinese") && dto.getStyle().equals("CULTURE")) {
                return dto;
            }
        }
        return null;
    }

    private List<LabelTextDto> otherLanguageLabelTextDto(List<LabelTextDto> list) {
        List<LabelTextDto> result = Lists.newArrayList();
        for (LabelTextDto dto : list) {
            if (!dto.getLanguage().equals("Chinese")) {
                result.add(dto);
            }
        }
        return result;
    }


}
