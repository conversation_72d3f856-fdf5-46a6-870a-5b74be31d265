package com.iciyun.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.iciyun.common.core.domain.LabelTextDto;
import com.iciyun.common.core.domain.entity.ChatRespDto;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.utils.ConcurrentQueueUtil;
import com.iciyun.common.utils.HelpMe;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.domain.dto.TranslaterData;
import com.iciyun.system.domain.dto.TranslaterItemData;
import com.iciyun.system.mapper.ScenicSpotCustomizeMapper;
import com.iciyun.system.mapper.SysTouristLabelMapper;
import com.iciyun.system.service.IGuideTkService;
import com.iciyun.system.service.impl.ChatService;
import com.iciyun.task.dto.DealOtherLanguageDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 外文文本和音频任务
 * <AUTHOR> on 2025-07-05 19:30.
 */
@Slf4j
@Component
public class DealOtherLanguageTask {

    @Autowired
    ScenicSpotCustomizeMapper scenicSpotCustomizeMapper;
    @Autowired
    SysTouristLabelMapper sysTouristLabelMapper;
    @Autowired
    GenAudioBySexTask genAudioBySexTask;
    @Autowired
    private IGuideTkService guideTkService;
    @Autowired
    private RedisCache redisCache;
    //cozeToken
    private static String cozeTokenKey = "chat:cozeToken";
    //14 分钟
    private static int cozeTokenTime = 14;

    ConcurrentQueueUtil<DealOtherLanguageDto> concurrentQueueUtil = new ConcurrentQueueUtil<DealOtherLanguageDto>();

    public void enqueue(DealOtherLanguageDto dealOtherLanguageDto) {
        concurrentQueueUtil.enqueue(dealOtherLanguageDto);
    }

    public void clear() {
        concurrentQueueUtil.clear();
    }

    public int size() {
        return concurrentQueueUtil.size();
    }


    public void run(){
        if (concurrentQueueUtil.isEmpty()) {
            return;
        }
        DealOtherLanguageDto dto = null;
        try {
            dto = concurrentQueueUtil.dequeue();
        } catch (Exception e) {
        }
        if (dto==null){
            return;
        }

        this.dealOtherLanguage(dto.getScenicId(),dto.getSysTouristLabel(),dto.getLabelTextDto());
    }


    /**
     * 处理非中文的文本及语音生成
     * @param scenicId
     * @param sysTouristLabel 为 null 表示处理景区简介
     * @param labelTextDto 中文的默认风格：文化内涵
     */
    private void dealOtherLanguage(Integer scenicId, SysTouristLabel sysTouristLabel, LabelTextDto labelTextDto) {
        ScenicSpot scenicSpot = scenicSpotCustomizeMapper.selectById(scenicId);
        ScenicSpot.StyleAndLanguage styleAndLanguage = scenicSpot.parseStyleAndLanguage();
        List<String> languageList = styleAndLanguage.getLanguage();
        languageList = languageList.stream().filter(item->{
            return !item.equals("Chinese");
        }).collect(Collectors.toList());

        if (CollUtil.isEmpty(languageList)) {
            return;
        }

        List<String> tempList = Lists.newArrayList();
        for (String item:languageList){
            String temp = TranslaterData.transLangMap.get(item);
            tempList.add(temp);
        }

        String msg = "翻译成 [";
        for (String item : tempList) {
            msg += item;
            msg += ",";
        }
        msg = StrUtil.removeSuffix(msg, ",");
        msg += "]：" + labelTextDto.getLabelText();

        log.info("翻译成外文，user prompt --> {}", msg);

        ChatService chatService = new ChatService(getToken(), "7522786915092545546");
        ChatRespDto chatRespDto = null;
        try {
            chatRespDto = chatService.translaterChat(msg);
        } catch (Exception e) {
        }
        if (chatRespDto == null) {
            return;
        }
        JSONObject obj = JSONUtil.parseObj(chatRespDto.getContent());
        TranslaterData translaterData = JSONUtil.toBean(obj, TranslaterData.class);

        List<LabelTextDto> newDtoList = Lists.newArrayList();
        for (String language : languageList) {
            LabelTextDto dto = new LabelTextDto();
            BeanUtil.copyProperties(labelTextDto, dto, HelpMe.copyOptions());
            dto.setLanguage(language);
            dto.setUpdateTimeStr(DateUtil.now());

            TranslaterItemData translaterItemData = translaterData.filter(language);
            dto.setLabelText(translaterItemData.getTranslation());
            newDtoList.add(dto);
        }
        if (CollUtil.isEmpty(newDtoList)) {
            return;
        }

        Map<String, LabelTextDto> map = HelpMe.transList2Map(newDtoList, LabelTextDto::getLanguage);

        String oldLabelText = "";
        if (sysTouristLabel == null) {
            //重新再查询一次
            scenicSpot = scenicSpotCustomizeMapper.selectById(scenicId);
            oldLabelText = scenicSpot.getLabelText();
        } else {
            //重新再查询一次
            sysTouristLabel = sysTouristLabelMapper.selectSysTouristLabelById(sysTouristLabel.getId());
            oldLabelText = sysTouristLabel.getLabelText();
        }

        List<LabelTextDto> oldList = HelpMe.parseLabelText(oldLabelText);
        List<LabelTextDto> list = Lists.newArrayList();
        for (LabelTextDto old : oldList) {
            String language = old.getLanguage();
            if (!map.containsKey(language)) {
                list.add(old);
            }
        }
        list.addAll(newDtoList);

        //更新文本
        if (sysTouristLabel == null) {
            scenicSpot.setLabelText(JSONUtil.toJsonStr(list));
            scenicSpotCustomizeMapper.updateById(scenicSpot);
        } else {
            sysTouristLabel.setLabelText(JSONUtil.toJsonStr(list));
            sysTouristLabel.setJoinCluster(sysTouristLabel.getJoinCluster());
            sysTouristLabelMapper.updateSysTouristLabel(sysTouristLabel);
        }

        //生成音频
        for (LabelTextDto dto : newDtoList) {
            if (scenicSpot.getStyleAndLanguageSwitch()==0){
                genAudioBySexTask.enqueue(dto);
            }else if (scenicSpot.getStyleAndLanguageSwitch()==1){
                genAudioBySexTask.enqueue_coze(dto);
            }
        }
    }


    private String getToken() {
        String cozeToken = redisCache.getCacheObject(cozeTokenKey);
        if (StringUtils.isBlank(cozeToken)) {
            cozeToken = guideTkService.getToken();
            redisCache.setCacheObject(cozeTokenKey, cozeToken, cozeTokenTime, TimeUnit.MINUTES);
        }
        return cozeToken;
    }

}
