package com.iciyun.task;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.iciyun.common.core.domain.LabelTextDto;
import com.iciyun.common.core.domain.entity.SysDictData;
import com.iciyun.common.core.domain.model.UserStyleDto;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.utils.ConcurrentQueueUtil;
import com.iciyun.common.utils.HelpMe;
import com.iciyun.common.utils.tts.TTSClient;
import com.iciyun.system.service.GenAudioService;
import com.iciyun.system.service.ISysDictDataService;
import com.iciyun.system.service.ISysUserService;
import com.iciyun.system.service.ScenicCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * PC 后台，手动操作生成解说词，解说词音频生成任务
 * <AUTHOR> on 2025-06-29 09:34.
 */
@Slf4j
@Service
public class GenAudioBySexTask {

    ConcurrentQueueUtil<LabelTextDto> concurrentQueueUtil = new ConcurrentQueueUtil<LabelTextDto>();

    @Autowired
    ISysUserService sysUserService;
    @Autowired
    ScenicCacheManager scenicCacheManager;
    @Autowired
    GenAudioService genAudioService;
    @Autowired
    RedisCache redisCache;
    @Autowired
    ISysDictDataService sysDictDataService;

    private Boolean isWorking = false;

    public Boolean isWorking() {
        return isWorking;
    }

    public void enqueue(LabelTextDto labelTextDto) {
        concurrentQueueUtil.enqueue(labelTextDto);
    }

    public void clear() {
        concurrentQueueUtil.clear();
    }

    public int size() {
        return concurrentQueueUtil.size();
    }


    // 生成音频
    public void run__1(){
        doJob();
    }

    // 生成音频
    public void run__2(){
        doJob();
    }

    // 生成音频
    public void run__3(){
        doJob();
    }

    // 生成音频
    public void run__4(){
        doJob();
    }

    // 生成音频
    public void run__5(){
        doJob();
    }

    // 生成音频
    public void run__6(){
        doJob();
    }

    //5090 生成音频
    public void run(){
        doJob();
    }

    public static void main(String[] args) {
        String str = "普通话";
        if (str.contains("女")){
            System.out.println("女");
        }else {
            System.out.println("男女");
        }
    }

    private void doJob(){
        if (concurrentQueueUtil.isEmpty()) {
            return;
        }
        isWorking = true;

        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType("sys_language");
        List<SysDictData> tempList = sysDictDataService.selectDictDataList(sysDictData);

        LabelTextDto labelTextDto = null;
        try {
            labelTextDto = concurrentQueueUtil.dequeue();
        } catch (Exception e) {
        }

        if (labelTextDto==null){
            return;
        }

        for (SysDictData item:tempList){
            if (labelTextDto.getLanguage().equals(item.getDictValue())){
                String remark = item.getRemark();
                if (remark.contains("女")){//仅支持女生音色
                    genAudioBySex(labelTextDto,"GIRL");
                }else {
                    genAudioBySex(labelTextDto,"GIRL");
                    genAudioBySex(labelTextDto,"BOY");
                }
            }
        }

        isWorking = false;
    }




    private Boolean genAudioBySex(LabelTextDto labelTextDto, String sexType) {

        UserStyleDto userStyleDto = new UserStyleDto();
        userStyleDto.setSexType(sexType);
        userStyleDto.setLanguageFlag(labelTextDto.getLanguage());
        userStyleDto.setGuideStyle(labelTextDto.getStyle());

        String scenicId = labelTextDto.getTouristId();
        String scenicLabel = labelTextDto.getLabelName();
        String scenicName = labelTextDto.getTouristName();
        String voiceId = sysUserService.getVoiceId(userStyleDto);
        String content = labelTextDto.getLabelText();

        String labelKay = scenicCacheManager.getCacheKey(userStyleDto, scenicId, scenicLabel, voiceId);

        if (content.contains("https://github.com/liu-qi-666/liu-qi-666.github.io")) {
            return true;
        }

        content = HelpMe.replaceContent(content);

        ThreadUtil.safeSleep(1000);

        String filePath = genAudioService.genAudio(labelTextDto.getLanguage(),voiceId,content);

        if (StrUtil.isNotEmpty(filePath)) {
            if (StrUtil.isNotEmpty(labelTextDto.getLabelName())) {
                log.info("重新缓存 [{}] 的 [{}]，key ：{}，labelTextDto：{}", scenicName, labelTextDto.getLabelName(), labelKay, labelTextDto);
            } else {
                log.info("重新缓存 [{}] 的 简介，key ：{}，labelTextDto：{}", scenicName, labelKay, labelTextDto);
            }
            redisCache.setCacheObject(labelKay, filePath);
            return true;
        }
        return false;
    }


}
