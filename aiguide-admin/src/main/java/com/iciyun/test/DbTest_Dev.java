package com.iciyun.test;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import cn.hutool.db.ds.simple.SimpleDataSource;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iciyun.common.core.domain.LabelTextDto;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.system.domain.dto.GenTextAgainParam;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.sql.DataSource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.File;
import java.io.FileWriter;
import java.nio.charset.Charset;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2025-03-19 16:34.
 */
@Slf4j
public class DbTest_Dev {

    public static DataSource ds = new SimpleDataSource("********************************************", "pg", "pg123.");

    @Test
    public void 上传文件() {
        File file = new File("/Users/<USER>/Desktop/test/onubfkgie6.txt");

        String url = upload2Oss(file.getAbsolutePath());

        System.out.println(url);

    }



    @Test
    public void 生成解说词_简介() throws Exception{
        File file = new File("/Users/<USER>/Desktop/test/temp.txt");

        List<String> list = FileUtil.readUtf8Lines(file);

        String url = "https://test.guide.iciyun.net/prod-api/guide/cache/genDetailText?scenicId={}";

        for (int i = 0; i < list.size(); i++) {
            String one = list.get(i);
            String[] arr = one.split(" ");
            String name = arr[0];
            String id = arr[1];

            String tempUrl = StrUtil.format(url,id);
            String result = HttpUtil.get(tempUrl);
            log.info("景区：{}，ID:{}, 生成解说词 result: {}",name,id,result);

            ThreadUtil.safeSleep(100);
        }

    }


    @Test
    public void 生成解说词_Label() throws Exception{
        File file = new File("/Users/<USER>/Desktop/test/temp.txt");

        List<String> list = FileUtil.readUtf8Lines(file);

        String url = "https://test.guide.iciyun.net/prod-api/guide/cache/genLabelText";

        for (int i = 0; i < list.size(); i++) {
            String one = list.get(i);
            String[] arr = one.split(" ");
            String name = arr[0];
            String id = arr[1];

            Map<String,Object> map = Maps.newHashMap();
            map.put("scenicId",Long.parseLong(id));
            map.put("detailAddress","");
            String result = HttpUtil.post(url, JSONUtil.toJsonStr(map));

            log.info("景区：{}，ID:{}, 生成解说词 result: {}",name,id,result);
            ThreadUtil.safeSleep(100);
        }

    }


    @Test
    public void 重新缓存_Label() throws Exception{
        List<String> nameList = Lists.newArrayList();
        nameList.add("测试水南庄");

        List<String> styleList = Lists.newArrayList("CULTURE");

        for (String name:nameList){
            String sql = "select id,tourist_id,tourist_name,label_name,cache_ok,audio_cache_ok,label_text from sys_tourist_label where tourist_name = '"+name+"'";
            List<Entity> list = DbUtil.use(ds).query(sql);
            for (Entity entity:list){
                String label_text = entity.getStr("label_text");
                List<LabelTextDto> tempList = parseLabelText(label_text);
                for (LabelTextDto dto:tempList){
                    if(styleList.contains(dto.getStyle()) && "Chinese".equals(dto.getLanguage())){
                        dealAudio(entity,dto);
                    }
                }
            }
        }

    }

    @Test
    public void 重新缓存_简介() throws Exception{
        List<String> nameList = Lists.newArrayList();
        nameList.add("测试水南庄");

        List<String> styleList = Lists.newArrayList("CULTURE");

        for (String name:nameList){
            String sql = "select id,name,label_text from scenic_spot where name = '"+name+"'";
            List<Entity> list = DbUtil.use(ds).query(sql);
            for (Entity entity:list){
                String label_text = entity.getStr("label_text");
                List<LabelTextDto> tempList = parseLabelText(label_text);
                for (LabelTextDto dto:tempList){
                    if(styleList.contains(dto.getStyle()) && "Chinese".equals(dto.getLanguage())){
                        dealAudio_简介(entity,dto);
                    }
                }
            }
        }

    }


    private void dealAudio_简介(Entity entity,LabelTextDto dto){
        String tourist_name = entity.getStr("name");
        String style = dto.getStyle();
        String content = dto.getLabelText();

        GenTextAgainParam param = new GenTextAgainParam();
        param.setScenicId(Long.parseLong(entity.getStr("id")));
        param.setLabelId(0L);
        param.setStyle(style);
        param.setLanguage("Chinese");
        param.setContent(content);
        param.setType(2);

        String url = "https://test.guide.iciyun.net/prod-api/guide/cache/genTextAgain";

        String result = HttpUtil.post(url, JSONUtil.toJsonStr(param));
        JSONObject jsonObject = JSONUtil.parseObj(result);
        Integer code = jsonObject.getInt("code");

        log.info("重新生成简介音频，景区：{}，style：{}，code：{}，content：{}",tourist_name,style,code,content);
    }

    private void dealAudio(Entity entity,LabelTextDto dto){
        String tourist_name = entity.getStr("tourist_name");
        String label_name = entity.getStr("label_name");
        String style = dto.getStyle();
        String content = dto.getLabelText();

        GenTextAgainParam param = new GenTextAgainParam();
        param.setScenicId(Long.parseLong(entity.getStr("tourist_id")));
        param.setLabelId(entity.getLong("id"));
        param.setStyle(style);
        param.setLanguage("Chinese");
        param.setContent(content);
        param.setType(2);

        String url = "https://test.guide.iciyun.net/prod-api/guide/cache/genTextAgain";

        String result = HttpUtil.post(url, JSONUtil.toJsonStr(param));
        JSONObject jsonObject = JSONUtil.parseObj(result);
        Integer code = jsonObject.getInt("code");

        log.info("重新生成label音频，景区：{}，label：{}，style：{}，code：{}，content：{}",tourist_name,label_name,style,code,content);
    }


    private List<LabelTextDto> parseLabelText(String labelText) {
        JSONArray arr = null;
        try {
            arr = JSONUtil.parseArray(labelText);
        } catch (Exception e) {
        }
        List<LabelTextDto> dtoList = Lists.newArrayList();
        if (arr==null)return dtoList;
        for (Object obj : arr) {
            JSONObject jsonObject = JSONUtil.parseObj(obj);
            LabelTextDto dto = JSONUtil.toBean(jsonObject, LabelTextDto.class);
            dtoList.add(dto);
        }
        return dtoList;
    }

    @Test
    public void test01() {
        String sql = "select id from scenic_spot where cache_ok = 1";

        try {
            List<Entity> list = DbUtil.use(ds).query(sql);
            File file = new File("/Users/<USER>/Desktop/test/id.txt");
            FileUtil.touch(file);

            List<String> idList = list.stream().map(item -> {
                return item.getStr("id");
            }).collect(Collectors.toList());

            FileUtil.appendUtf8Lines(idList,file);

        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }


    @Test
    public void 更新景区缩略图_批量() {
        String path = "/Users/<USER>/Desktop/test/abc";

        List<File> fileList = FileUtil.loopFiles(path);

        for (File item : fileList) {
            String name = item.getName();
            String extName = FileUtil.extName(item);
            name = StrUtil.removeSuffix(name, "." + extName);

            String sql = "select * from scenic_spot where name = '" + name + "' limit 1";

            try {
                Entity entity = DbUtil.use(ds).queryOne(sql);
                if (entity != null) {

                    String name2 = name + "_缩略图." + extName;

                    String absPath = item.getAbsolutePath();

                    String absPath2 = StrUtil.removeSuffix(absPath, item.getName()) + name2;

                    FileUtil.copy(absPath, absPath2, true);

                    String url = upload2Oss(absPath2);
                    sql = "update scenic_spot set thumbnail = '" + url + "' where name = '" + name + "'";
                    int count = DbUtil.use(ds).execute(sql);
                    log.info("景区名称：{} 更新景区缩略图... {}", name, count);
                } else {
                    log.info("通过景区名称：{} 未查询到数据", name);
                }
            } catch (SQLException e) {
                log.error("执行 sql 失败！", e);
            }
        }

    }


    @Test
    public void test1() {
        String xmlPath = "/Users/<USER>/Desktop/test/a/Annotations/55e040a9-阿坝州.xml";
        String jsonPath = "/Users/<USER>/Desktop/test/output.json";

        System.out.println(123);
    }


    @Test
    public void test2() {

        dealExcel();
    }


    @Test
    public void test3() {
        String txtPath = "/Users/<USER>/Desktop/test/test.txt";
        String failTxtPath = "/Users/<USER>/Desktop/test/fail.txt";
        dealThumbnail(txtPath, failTxtPath);
    }


    @Test
    public void 景区标注ZIP() {
        String zipDir = "/Users/<USER>/Desktop/test";
        List<File> zipList = FileUtil.loopFiles(zipDir);

        zipList = zipList.stream().filter(item -> {
            return FileUtil.extName(item).equalsIgnoreCase("zip");
        }).collect(Collectors.toList());

        for (File zip : zipList) {
            dealZip(zip.getAbsolutePath());
            ThreadUtil.safeSleep(1000);
        }

        String url = "https://test.guide.iciyun.net/prod-api/test/user/resetTouristMap";
        HttpUtil.get(url);

    }


    @Test
    public void test5() {
        String dir = "/Users/<USER>/Desktop/test/circle-color.PNG";

        upload2Oss(dir);
    }


    @Test
    public void 上传缩略图() {
        String name = "猎玩通航小镇";
        String img = "/Users/<USER>/Desktop/test/猎玩通航小镇缩略图.png";
        String url = upload2Oss(img);

        String sql = "update scenic_spot set thumbnail = '" + url + "' where name = '" + name + "'";

        try {
            int count = DbUtil.use(ds).execute(sql);
            log.info("结果：{}, 执行的 sql : {}", count, sql);
        } catch (SQLException e) {
            log.error("执行 sql 失败！", e);
        }

    }


    @Test
    public void 用户风格老数据处理() {

        String sql = "select * from sys_user";
        List<SysUser> list = null;
        try {
            list = DbUtil.use(ds).query(sql, SysUser.class);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        for (SysUser item : list) {
            String hobby = item.getHobbyTypes();
            System.out.println(hobby);
            JSONArray arr = JSONUtil.parseArray(hobby);
            if (arr.size() > 1) {
                sql = "update sys_user set hobby_types = '[\"CULTURE\"]' where user_id = '" + item.getUserId() + "'";
                try {
                    int count = DbUtil.use(ds).execute(sql);
                    log.info("结果：{}, 执行的 sql : {}", count, sql);
                } catch (SQLException e) {
                    log.error("执行 sql 失败！", e);
                }
            }
        }

    }


    @Test
    public void 上传导览图() {
        String name = "颐和园";
        String img = "/Users/<USER>/Desktop/test/颐和园.jpg";
        String url = upload2Oss(img);

        String sql = "update scenic_spot set guide_map = '" + url + "' where name = '" + name + "'";

        try {
            int count = DbUtil.use(ds).execute(sql);
            log.info("结果：{}, 执行的 sql : {}", count, sql);
        } catch (SQLException e) {
            log.error("执行 sql 失败！", e);
        }

    }


    @Test
    public void test6() {
        resetTouristLabel("曹湾山遗址博物馆");
    }

    public static void resetTouristLabel(String name) {
        String sql = "update scenic_spot set label_flag = 0 where name = '" + name + "'";
        String sql2 = "delete from sys_tourist_label where tourist_id = (select id from scenic_spot where name = '" + name + "')";

        try {
            int count = DbUtil.use(ds).execute(sql);
            log.info("结果：{}, 执行的 sql : {}", count, sql);

            count = DbUtil.use(ds).execute(sql2);
            log.info("结果：{}, 执行的 sql : {}", count, sql2);
        } catch (SQLException e) {
            log.error("执行 sql 失败！", e);
        }
    }

    public static String upload2Oss(String url, String destFile) {
        long size = HttpUtil.downloadFile(url, destFile);

//        String uploadUrl = "https://test.guide.iciyun.net/prod-api/guide/user/upFiles";
        String uploadUrl = "http://localhost:9898/prod-api/guide/user/upFiles";
        if (size > 0) {
            Map<String, Object> map = new HashMap<>();
            map.put("file", new File(destFile));
            String result = HttpUtil.post(uploadUrl, map);
            log.info("上传结果：{}", result);

            JSONObject obj = JSONUtil.parseObj(result);
            int code = obj.getInt("code");
            if (code == 200) {
                String ossUrl = obj.getStr("data");
                return ossUrl;
            }
        }
        return "";
    }

    public static String upload2Oss(String destFile) {

        String uploadUrl = "https://test.guide.iciyun.net/prod-api/guide/user/upFiles";
//        String uploadUrl = "http://localhost:9898/prod-api/guide/user/upFiles";
        Map<String, Object> map = new HashMap<>();
        map.put("file", new File(destFile));
        String result = HttpUtil.post(uploadUrl, map);
        log.info("上传结果：{}", result);

        JSONObject obj = JSONUtil.parseObj(result);
        int code = obj.getInt("code");
        if (code == 200) {
            String ossUrl = obj.getStr("data");
            return ossUrl;
        }
        return "";
    }


    public static void dealThumbnail(String txtPath, String failTxtPath) {

        List<String> lineList = FileUtil.readUtf8Lines(txtPath);

        for (String item : lineList) {
            JSONObject jsonObject = JSONUtil.parseObj(item);
            String name = jsonObject.getStr("name");
            String url = jsonObject.getStr("url");

            if (url.startsWith("https://space.coze.cn/web")) {
                List<String> tempList = Lists.newArrayList();
                tempList.add(name);
                FileUtil.writeUtf8Lines(tempList, FileUtil.file(failTxtPath));
            } else {
                String destFile = "/Users/<USER>/Desktop/test/" + name + ".jpg";
                String ossUrl = upload2Oss(url, destFile);

                if (StrUtil.isNotEmpty(ossUrl)) {
                    String sql = "update scenic_spot set thumbnail = '" + ossUrl + "' where name = '" + name + "'";
                    try {
                        int count = DbUtil.use(ds).execute(sql);
                        log.info("结果：{}, 执行的 sql : {}", count, sql);
                    } catch (SQLException e) {
                        log.error("执行 sql 失败！", e);
                    }
                }

            }
        }

    }


    public static void main3(String[] args) {
        String zipDir = "/Users/<USER>/Desktop/test";
        List<File> zipList = FileUtil.loopFiles(zipDir);

        zipList = zipList.stream().filter(item -> {
            return FileUtil.extName(item).equalsIgnoreCase("zip");
        }).collect(Collectors.toList());

        for (File zip : zipList) {
            dealZip(zip.getAbsolutePath());
            ThreadUtil.safeSleep(1000);
        }

        String url = "https://test.guide.iciyun.net/prod-api/test/user/resetTouristMap";
        HttpUtil.get(url);

    }


    public static void dealZip(String zip) {
        File file = ZipUtil.unzip(zip);
        String name = file.getName();

        // 调用方法查找第一个 XML 文件
        String firstXmlPath = findFirstXmlFile(file.getAbsolutePath());

        // 输出结果
        if (firstXmlPath != null) {
            log.info("当前景区名称：{}，xml 文件路径 ：{}", name, firstXmlPath);
        } else {
            log.info("当前景区名称：{},在指定目录及其子目录中未找到 XML 文件", name);
        }

        if (firstXmlPath != null) {
            String jsonPath = "/Users/<USER>/Desktop/test/" + name + ".json";
            dealXml(firstXmlPath, jsonPath);

            String sql = "select id from scenic_spot where label_flag = 0 and name = '" + name + "' limit 1";

            try {
                Entity entity = DbUtil.use(ds).queryOne(sql);
                if (entity != null) {
                    String id = entity.getStr("id");
                    dealJson(id, name, jsonPath);
                } else {
                    log.info("通过名称 {} 及 label_flag 标识，未查询到景区 ID！", name);
                }
            } catch (SQLException e) {
                log.error("{},查询景区ID失败！", name, e);
                throw new RuntimeException(e);
            }
        }


    }

    public static String findFirstXmlFile(String directoryPath) {
        File directory = new File(directoryPath);
        AtomicReference<String> firstXmlPath = new AtomicReference<>(); // 用于存储找到的第一个 XML 文件路径

        // 递归查找文件
        listFiles(directory, firstXmlPath);

        return firstXmlPath.get();
    }

    private static void listFiles(File directory, AtomicReference<String> firstXmlPath) {
        // 如果已经找到文件，停止递归
        if (firstXmlPath.get() != null) {
            return;
        }

        // 如果是文件夹，遍历其中的文件
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    listFiles(file, firstXmlPath); // 递归调用
                }
            }
        } else {
            // 如果是文件，检查是否是 XML 文件
            if (directory.getName().endsWith(".xml")) {
                firstXmlPath.set(directory.getAbsolutePath()); // 存储第一个找到的 XML 文件路径
            }
        }
    }


    public static void dealJson(String tourist_id, String tourist_name, String jsonPath) {
        List<String> lineList = FileUtil.readUtf8Lines(jsonPath);

        String create_time = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);

        String tempSql = "INSERT INTO sys_tourist_label (tourist_id, tourist_name, label_name, image_width, image_height, x1y1, x2y2, create_by, create_time, remark) VALUES " +
                "('{tourist_id}','{tourist_name}', '{label_name}', '{image_width}','{image_height}','{x1y1}','{x2y2}','{create_by}','{create_time}',NULL)";

        Map<String, String> param = Maps.newHashMap();

        param.put("tourist_id", tourist_id);
        param.put("tourist_name", tourist_name);
        param.put("create_time", create_time);
        param.put("create_by", "system");

        int i = 0;
        for (String line : lineList) {
            JSONObject json = JSONUtil.parseObj(line);
            String name = json.getStr("name");
            String xmin = json.getStr("xmin");
            String ymin = json.getStr("ymin");
            String xmax = json.getStr("xmax");
            String ymax = json.getStr("ymax");

            param.put("label_name", name);
            param.put("image_width", "0");
            param.put("image_height", "0");
            param.put("x1y1", xmin + "," + ymin);
            param.put("x2y2", xmax + "," + ymax);


            String sql = StrUtil.format(tempSql, param);
            try {
                int count = DbUtil.use(ds).execute(sql);
                log.info("当前第 {} 个，执行 sql : {}", (++i), sql);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }

        }

        String sql = "update scenic_spot set label_flag = 1 where id = " + tourist_id;

        try {
            int count = DbUtil.use(ds).execute(sql);
            log.info("更新景区 label_flag 标识，执行 sql : {}", sql);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

    }

    @Test
    public void tempDealExcel() {
        ExcelReader reader = ExcelUtil.getReader("/Users/<USER>/Desktop/test/query_824851537656_2025-05-05_22_41_12.xlsx");
        List<List<Object>> readAll = reader.read();
        reader.close();

        Map<String, Object> map = new HashMap<>();
        for (int i = 0; i < readAll.size(); i++) {

            if (i == 0) continue;

            List<Object> temp = readAll.get(i);

            String userId = temp.get(3).toString();

            map.put(userId, userId);
        }


        System.out.println(map.size());

    }

    public static void dealExcel() {
        ExcelReader reader = ExcelUtil.getReader("/Users/<USER>/Desktop/test/labels_my-project-name_2025-04-09-03-19-27.xlsx");
        List<List<Object>> readAll = reader.read();
        reader.close();

        String tourist_id = "21";
        String tourist_name = "故宫";
        String create_time = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);

        String tempSql = "INSERT INTO ai-guide.sys_tourist_label (tourist_id, tourist_name, label_name, image_width, image_height, x1y1, x2y2, create_by, create_time, remark) VALUES " +
                "('{tourist_id}','{tourist_name}', '{label_name}', '{image_width}','{image_height}','{x1y1}','{x2y2}','{create_by}','{create_time}',NULL)";

        Map<String, String> param = Maps.newHashMap();

        param.put("tourist_id", tourist_id);
        param.put("tourist_name", tourist_name);
        param.put("create_time", create_time);
        param.put("create_by", "system");

        for (int i = 0; i < readAll.size(); i++) {
            if (i == 0) continue;
            List<Object> temp = readAll.get(i);
            String label_name = temp.get(0).toString();
            String x1 = temp.get(1).toString();
            String y1 = temp.get(2).toString();

            String temp1 = temp.get(3).toString();
            String temp2 = temp.get(4).toString();

            int x2 = Integer.parseInt(x1) + Integer.parseInt(temp1);
            int y2 = Integer.parseInt(y1) + Integer.parseInt(temp2);

            String image_width = temp.get(6).toString();
            String image_height = temp.get(7).toString();

            param.put("label_name", label_name);
            param.put("image_width", image_width);
            param.put("image_height", image_height);
            param.put("x1y1", x1 + "," + y1);
            param.put("x2y2", x2 + "," + y2);


            String sql = StrUtil.format(tempSql, param);
            try {
                int count = DbUtil.use(ds).execute(sql);
                log.info("当前第 {} 个，执行 sql : {}", (i + 1), sql);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }

    }


    public static void insertTxt() {
        String tourist_id = "2";
        String tourist_name = "恭王府";
        String create_time = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);

        String tempSql = "insert into sys_tourist_txt(tourist_id,tourist_name,title,content,create_time) values('{tourist_id}','{tourist_name}','{title}','{content}','{create_time}')";

        Map<String, String> param = Maps.newHashMap();

        param.put("tourist_id", tourist_id);
        param.put("tourist_name", tourist_name);
        param.put("create_time", create_time);

        String path = "/Users/<USER>/Desktop/temp/data.txt";

        JSONArray arr = JSONUtil.parseArray(FileUtil.readUtf8String(path));

        for (int i = 0; i < arr.size(); i++) {
            Object data = arr.get(i);
            JSONObject json = JSONUtil.parseObj(data);
            String title = json.getStr("title");
            String content = json.getStr("content");
            param.put("title", title);
            param.put("content", content);

            String sql = StrUtil.format(tempSql, param);
            try {
                int count = DbUtil.use(ds).execute(sql);
                log.info("当前第 {} 个，执行 sql : {}", (i + 1), sql);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }

    }


    public static void insertPhoto() {
        String tourist_id = "2";
        String tourist_name = "恭王府";
        String create_time = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);

        String tempSql = "insert into sys_tourist_photo(tourist_id,tourist_name,url,create_time) values('{tourist_id}','{tourist_name}','{url}','{create_time}')";

        Map<String, String> param = Maps.newHashMap();

        param.put("tourist_id", tourist_id);
        param.put("tourist_name", tourist_name);
        param.put("create_time", create_time);

        String path = "/Users/<USER>/Desktop/temp/urlData.txt";

        List<String> lineList = FileUtil.readUtf8Lines(path);

        for (int i = 0; i < lineList.size(); i++) {
            param.put("url", lineList.get(i));
            String sql = StrUtil.format(tempSql, param);
            try {
                int count = DbUtil.use(ds).execute(sql);
                log.info("当前第 {} 个，执行 sql : {}", (i + 1), sql);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }

    }

    public static void dealFile() {
        String dir = "/Users/<USER>/Desktop/test2/pic";

        List<File> list = FileUtil.loopFiles(dir);
        List<File> delList = Lists.newArrayList();

        for (File file : list) {
            if (file.getName().contains("hd")) {
            } else {
                FileUtil.del(file);
            }
        }
    }

    public static void query() {

        List<Entity> list = Lists.newArrayList();

        try {
            list = DbUtil.use(ds).query("select * from sys_config");
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }


        log.info("数据：{}", list);

    }


    public static void dealXml(String xmlPath, String jsonPath) {
        try {
            // 输入输出文件路径
            File xmlFile = new File(xmlPath);
            File outputFile = new File(jsonPath);

            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(xmlFile);
            doc.getDocumentElement().normalize();

            NodeList objectList = doc.getElementsByTagName("object");
            List<String> jsonLines = new ArrayList<>();

            // 处理每个object节点
            for (int i = 0; i < objectList.getLength(); i++) {
                Element object = (Element) objectList.item(i);
                jsonLines.add(convertToSimplifiedJson(object));
            }

            // 写入文件
            try (FileWriter writer = new FileWriter(outputFile)) {
                for (String json : jsonLines) {
                    writer.write(json + "\n");
                }
            }

            log.info("转换完成，输出文件: " + outputFile.getAbsolutePath());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String convertToSimplifiedJson(Element object) {
        Element bndbox = (Element) object.getElementsByTagName("bndbox").item(0);

        return String.format(
                "{" +
                        "\"name\": \"%s\"," +  // 名称保持字符串类型
                        "\"xmin\": %s," +       // 坐标转为数值类型
                        "\"ymin\": %s," +
                        "\"xmax\": %s," +
                        "\"ymax\": %s" +
                        "}",
                getCleanValue("name", object),
                getNumericValue("xmin", bndbox),
                getNumericValue("ymin", bndbox),
                getNumericValue("xmax", bndbox),
                getNumericValue("ymax", bndbox)
        );
    }

    // 获取文本内容（自动去空格）
    private static String getCleanValue(String tag, Element element) {
        NodeList nodeList = element.getElementsByTagName(tag);
        return nodeList.item(0).getTextContent().trim();
    }

    // 专门处理数值字段
    private static String getNumericValue(String tag, Element element) {
        String value = getCleanValue(tag, element);
        // 添加数字校验（可选）
        if (!value.matches("\\d+")) {
            System.err.println("非数值内容: " + tag + " = " + value);
        }
        return value;
    }


}
