package com.iciyun.test;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import cn.hutool.db.ds.simple.SimpleDataSource;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iciyun.common.core.domain.LabelTextDto;
import com.iciyun.common.utils.HelpMe;
import com.iciyun.common.utils.sign.Md5Utils;
import com.iciyun.system.domain.dto.GenAudioAgainParam;
import com.iciyun.system.domain.dto.ScenicPointParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.sql.DataSource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.File;
import java.io.FileWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2025-03-19 16:34.
 */
@Slf4j
public class DbTest_Pro {

    public static DataSource ds = new SimpleDataSource("*******************************************", "ideepyou", "ZbNyE2FBZExpubLr");


    @Test
    public void 线上数据统计() throws Exception {
        label交互统计();
        统计收款();
        扫码转换率统计();
        注册用户统计_按指定日期();
    }

    @Test
    public void 使用量及营业额统计() throws Exception {
        List<String> nameList = Lists.newArrayList();
        nameList.add("安丘青云山民俗游乐园");
        nameList.add("十笏园文化街区");
        nameList.add("潍坊市博物馆");
        nameList.add("潍坊十笏园博物馆");
        nameList.add("寒亭杨家埠民间艺术大观园");
        nameList.add("青州范公亭公园景区");
        nameList.add("沂蒙山沂山景区");
        nameList.add("金宝乐园");
        nameList.add("青州市博物馆");
        nameList.add("青州古城景区");
        nameList.add("昌邑青山秀水景区");
        nameList.add("巨淀湖风景区");
        nameList.add("林海生态博览园");
        nameList.add("诸城恐龙博物馆");
        nameList.add("诸城中国暴龙馆");
        nameList.add("诸城常山文化博物苑");
        nameList.add("诸城市动物园");

        String sql1 = """
                
                SELECT
                	scenic_name,
                	COALESCE ( SUM ( label_click_count ), 0 ) AS total_label_clicks,
                	COALESCE ( SUM ( speak_count ), 0 ) AS total_speaks,
                	COALESCE ( SUM ( lbs_count ), 0 ) AS total_lbs,
                	COALESCE ( SUM ( eye_count ), 0 ) AS total_eye,
                	COALESCE ( SUM ( label_click_count ), 0 ) + COALESCE ( SUM ( speak_count ), 0 ) + COALESCE ( SUM ( lbs_count ), 0 ) + COALESCE ( SUM ( eye_count ), 0 ) 总点击量,
                	COUNT ( 1 ) as 使用人数
                FROM
                	PUBLIC.sys_scenic_st
                WHERE
                	scenic_name = '{}'
                GROUP BY
                	scenic_name
                ORDER BY
                	COALESCE ( SUM ( label_click_count ), 0 ) + COALESCE ( SUM ( speak_count ), 0 ) + COALESCE ( SUM ( lbs_count ), 0 ) + COALESCE ( SUM ( eye_count ), 0 ) DESC
                
                """;

        String sql2 = """
                
                SELECT
                	oi.scenic_name,
                	SUM ( oi.order_amount ) amount
                FROM
                	guide_pay_order_item oi
                WHERE
                	oi.open_id IS NOT NULL
                	AND oi.scenic_name = '{}'
                GROUP BY
                	oi.scenic_name
                
                """;

        List<List<String>> rows = Lists.newArrayList();
        List<String> row0 = CollUtil.newArrayList("景区名称", "使用人数", "点击量", "营业额");
        rows.add(row0);

        for (String name:nameList){
            String tempSql1 = StrUtil.format(sql1,name);
            String tempSql2 = StrUtil.format(sql2,name);
            Entity entity = DbUtil.use(ds).queryOne(tempSql1);
            Entity entity2 = DbUtil.use(ds).queryOne(tempSql2);

            if (entity==null)continue;

            String 总点击量 = entity.getStr("总点击量");
            String 使用人数 = entity.getStr("使用人数");
            if(entity2 == null){
                entity2 = Entity.create();
                entity2.set("amount","0");
            }
            String amount = entity2.getStr("amount");

            List<String> row = CollUtil.newArrayList(name, 使用人数, 总点击量, amount);
            rows.add(row);
        }

        genXls(rows, "使用量及营业额统计", 20);

    }



    @Test
    public void 更新景区缓存标识() throws Exception{
        File file = new File("/Users/<USER>/Desktop/test/temp.txt");
        List<String> list = FileUtil.readUtf8Lines(file);

        for (int i = 0; i < list.size(); i++) {
            String one = list.get(i);
            String[] arr = one.split(" ");
            String name = arr[0];
            String id = arr[1];

            String sql = "update scenic_spot set audio_cache_ok = 1 where id = " + id;
            int count = DbUtil.use(ds).execute(sql);

            log.info("景区：{}，ID:{}, 影响行数 : {}",name,id,count);

            ThreadUtil.safeSleep(100);
        }

    }

    @Test
    public void 生成解说词_简介() throws Exception{
        File file = new File("/Users/<USER>/Desktop/test/temp.txt");

        List<String> list = FileUtil.readUtf8Lines(file);

        String url = "https://ai.ideepyou.com/prod-api/guide/cache/genDetailText?scenicId={}";

        for (int i = 0; i < list.size(); i++) {
            String one = list.get(i);
            String[] arr = one.split(" ");
            String name = arr[0];
            String id = arr[1];

            String tempUrl = StrUtil.format(url,id);
            String result = HttpUtil.get(tempUrl);
            log.info("景区：{}，ID:{}, 生成解说词 result: {}",name,id,result);

            ThreadUtil.safeSleep(100);
        }

    }


    @Test
    public void 生成解说词_Label() throws Exception{
        File file = new File("/Users/<USER>/Desktop/test/temp.txt");

        List<String> list = FileUtil.readUtf8Lines(file);

        String url = "https://ai.ideepyou.com/prod-api/guide/cache/genLabelText";

        for (int i = 0; i < list.size(); i++) {
            String one = list.get(i);
            String[] arr = one.split(" ");
            String name = arr[0];
            String id = arr[1];

            Map<String,Object> map = Maps.newHashMap();
            map.put("scenicId",Long.parseLong(id));
            map.put("detailAddress","");
            String result = HttpUtil.post(url, JSONUtil.toJsonStr(map));

            log.info("景区：{}，ID:{}, 生成解说词 result: {}",name,id,result);
            ThreadUtil.safeSleep(100);
        }

    }



    @Test
    public void 重新生成音频() throws Exception{
        //一次处理的景区个数
        Integer limit = 50;

        String sql = "select id,name from scenic_spot where cache_ok = 1 and audio_cache_ok = 0 limit "+limit;
//        String sql = "select id,name,label_text from scenic_spot where id = 5699 limit "+limit;

        String url = "https://ai.ideepyou.com/prod-api/guide/cache/genAudioAgain";

        List<Entity> list = DbUtil.use(ds).query(sql);
        for (Entity entity:list){
            String tourist_name = entity.getStr("name");

            GenAudioAgainParam param = new GenAudioAgainParam();
            param.setScenicId(entity.getInt("id"));


            String result = HttpUtil.post(url, JSONUtil.toJsonStr(param));
            JSONObject jsonObject = JSONUtil.parseObj(result);
            Integer code = jsonObject.getInt("code");

            log.info("重新生成音频，景区：{}，code：{}",tourist_name,code);


            sql = "update scenic_spot set audio_cache_ok = -1 where id = "+entity.getInt("id");
            DbUtil.use(ds).execute(sql);
            ThreadUtil.safeSleep(100);
        }


    }




    private List<LabelTextDto> parseLabelText(String labelText) {
        JSONArray arr = null;
        try {
            arr = JSONUtil.parseArray(labelText);
        } catch (Exception e) {
        }
        List<LabelTextDto> dtoList = Lists.newArrayList();
        if (arr==null)return dtoList;
        for (Object obj : arr) {
            JSONObject jsonObject = JSONUtil.parseObj(obj);
            LabelTextDto dto = JSONUtil.toBean(jsonObject, LabelTextDto.class);
            dtoList.add(dto);
        }
        return dtoList;
    }

    @Test
    public void 导出解说词() throws Exception {
        String touristId = "147";
        String sql = "select label_name,label_text from sys_tourist_label where tourist_id = " + touristId;

        List<Entity> list = DbUtil.use(ds).query(sql);

        String path = "/Users/<USER>/Desktop/test/天岳幕阜山.txt";
        File file = new File(path);


        Map<String,String> map = new HashMap<>();
        map.put("CHAT","聊天陪伴");
        map.put("HISTORY","历史考证");
        map.put("NATURE","自然漫步");
        map.put("CULTURE","文化内涵");
        map.put("POETRY","诗歌漫游");
        map.put("PARENT_CHILD","亲子教育");
        map.put("SCRIPT_IMMERSION","剧本沉浸");
        map.put("ART","艺术创作");

        for (Entity entity : list) {
            String labelName = entity.getStr("label_name");
            String labelText = entity.getStr("label_text");

            JSONArray arr = JSONUtil.parseArray(labelText);

            FileUtil.appendLines(Lists.newArrayList("《"+labelName+"》"), file,"utf-8");
            FileUtil.appendLines(Lists.newArrayList("\n"), file,"utf-8");

            for (Object obj : arr) {
                LabelTextDto labelTextDto = JSONUtil.toBean(cn.hutool.json.JSONUtil.parseObj(obj),LabelTextDto.class);
                FileUtil.appendLines(Lists.newArrayList(map.get(labelTextDto.getStyle())), file,"utf-8");
                FileUtil.appendLines(Lists.newArrayList(labelTextDto.getLabelText()), file,"utf-8");
                FileUtil.appendLines(Lists.newArrayList("\n\n"), file,"utf-8");
            }


        }
    }




    @Test
    public void asdf2() throws Exception {
        List<Long> idList = Lists.newArrayList(85969L, 85970L);

        Long id = 1782L;

        String sql = """
                update sys_tourist_label set audio_cache_ok = 0 where 
                	id in ({})
                """;
        String sqlIn = HelpMe.swap2SqlIn(idList);
        sql = StrUtil.format(sql, sqlIn);

        int count = DbUtil.use(ds).execute(sql);
        System.out.println(count);
    }


    @Test
    public void asdf() throws Exception {
        List<Long> idList = Lists.newArrayList(85969L, 85970L);

        Long id = 1782L;

        String sql = """
                SELECT
                  id,
                	tourist_id,
                	tourist_name,
                	label_name,
                	cache_ok,
                	audio_cache_ok,
                	label_text
                FROM
                	sys_tourist_label
                WHERE
                	id in ({})
                """;
        String sqlIn = HelpMe.swap2SqlIn(idList);
        sql = StrUtil.format(sql, sqlIn);

        List<Entity> entityList = DbUtil.use(ds).query(sql);
        for (Entity entity : entityList) {
            String scenicLabelHash = Md5Utils.hash(entity.getStr("label_name"));
            String tempStr = "scenic:" + id + "_" + scenicLabelHash + ":*";
            System.out.println(tempStr);
        }

    }


    /**
     * 把指定 label 的 8 种风格，中文语言，全部更新为统一的解说词
     *
     * @throws Exception
     */
    @Test
    public void 更新解说词() throws Exception {
        //sys_tourist_label 表的 id
        String id = "85969";

        //统一的解说词
        String newLabelText = """
                这里是十笏园文化街区西门，入口处矗立着醒目的“北海名区”牌坊。“北海”是潍坊的古称，源自汉代在此设立的北海郡，代表了潍坊悠久的历史底蕴。这座牌坊不仅标明了街区入口，更点明了整个街区的文化主题——它致力于再现古潍县（潍坊）作为“东莱首邑，北海名区”的繁华风貌，是您感受潍坊深厚历史与传统文化的地标起点。街区内关帝庙、郑板桥纪念馆、十笏园博物馆、文昌阁、陈介祺纪念馆等诸多景点，鲜活地浓缩了潍坊从民间信仰、廉政文化、园林艺术到科举教育、金石收藏的多元历史风貌，是您开启十笏园文化街区文脉深度探索的精彩起点。
                """;

        String sql = """
                SELECT
                  id,
                	tourist_id,
                	tourist_name,
                	label_name,
                	cache_ok,
                	audio_cache_ok,
                	label_text
                FROM
                	sys_tourist_label
                WHERE
                	id = '{}'
                """;

        sql = StrUtil.format(sql, id);

        Entity entity = DbUtil.use(ds).queryOne(sql);

        String label_text = entity.getStr("label_text");
        String label_name = entity.getStr("label_name");

        JSONArray tempArr = JSONUtil.parseArray(label_text);
        List<LabelTextDto> labelTextDtoList = tempArr.stream().map(item -> {
            JSONObject obj = JSONUtil.parseObj(item);
            LabelTextDto labelTextDto = new LabelTextDto();
            labelTextDto.setTouristId(obj.getStr("touristId"));
            labelTextDto.setTouristName(obj.getStr("touristName"));
            labelTextDto.setLabelName(label_name);
            labelTextDto.setLabelText(obj.getStr("labelText"));
            labelTextDto.setStyle(obj.getStr("style"));
            labelTextDto.setLanguage(obj.getStr("language"));
            return labelTextDto;
        }).collect(Collectors.toList());


        labelTextDtoList.stream().forEach(item -> {
            item.setLabelText(newLabelText);
        });

        sql = """
                update sys_tourist_label set audio_cache_ok = 0,label_text = '{}' where id = '{}'
                """;
        sql = StrUtil.format(sql, JSONUtil.toJsonStr(labelTextDtoList), id);
        DbUtil.use(ds).execute(sql);

    }


    @Test
    public void label交互统计() throws Exception {

        DateTime begin = DateUtil.beginOfDay(new Date());
        DateTime end = DateUtil.endOfDay(new Date());

        String beginStr = DateUtil.format(begin, DatePattern.NORM_DATETIME_PATTERN);
        String endStr = DateUtil.format(end, DatePattern.NORM_DATETIME_PATTERN);

        System.out.println(beginStr);
        System.out.println(endStr);

        String sql = """
                    SELECT
                    	scenic_name,
                    	COALESCE ( SUM ( label_click_count ), 0 ) AS total_label_clicks,
                    	COALESCE ( SUM ( speak_count ), 0 ) AS total_speaks,
                    	COALESCE ( SUM ( lbs_count ), 0 ) AS total_lbs,
                    	COALESCE ( SUM ( eye_count ), 0 ) AS total_eye,
                    	COALESCE ( SUM ( label_click_count ), 0 ) + COALESCE ( SUM ( speak_count ), 0 ) + COALESCE ( SUM ( lbs_count ), 0 ) + COALESCE ( SUM ( eye_count ), 0 ) total,
                    	count(1)
                    FROM
                    	PUBLIC.sys_scenic_st
                    WHERE
                    	create_time >= '{beginStr}'
                    	AND create_time <= '{endStr}'
                    GROUP BY
                    	scenic_name
                    ORDER BY
                    		COALESCE ( SUM ( label_click_count ), 0 ) + COALESCE ( SUM ( speak_count ), 0 ) + COALESCE ( SUM ( lbs_count ), 0 ) + COALESCE ( SUM ( eye_count ), 0 ) DESC
                """;

        Map<String, Object> param = Maps.newHashMap();
        param.put("beginStr", beginStr);
        param.put("endStr", endStr);

        sql = StrUtil.format(sql, param);

        List<Entity> list = DbUtil.use(ds).query(sql);

        List<List<String>> rows = Lists.newArrayList();
        List<String> row0 = CollUtil.newArrayList("景区名称", "label点击次数", "问小游次数", "LBS触发次数", "拍照识别次数", "次数总计", "几人在用");
        rows.add(row0);

        BigDecimal count0 = BigDecimal.ZERO;
        BigDecimal count1 = BigDecimal.ZERO;
        BigDecimal count2 = BigDecimal.ZERO;
        BigDecimal count3 = BigDecimal.ZERO;
        BigDecimal count4 = BigDecimal.ZERO;
        BigDecimal count5 = BigDecimal.ZERO;

        for (Entity entity : list) {

            String scenic_name = entity.getStr("scenic_name");

            BigDecimal total_label_clicks = entity.getBigDecimal("total_label_clicks");
            count1 = count1.add(total_label_clicks);

            BigDecimal total_speaks = entity.getBigDecimal("total_speaks");
            count2 = count2.add(total_speaks);

            BigDecimal total_lbs = entity.getBigDecimal("total_lbs");
            count3 = count3.add(total_lbs);

            BigDecimal total_eye = entity.getBigDecimal("total_eye");
            count4 = count4.add(total_eye);

            BigDecimal total = entity.getBigDecimal("total");
            count5 = count5.add(total);

            BigDecimal count = entity.getBigDecimal("count");
            count0 = count0.add(count);

            List<String> row = CollUtil.newArrayList(
                    scenic_name,
                    total_label_clicks.toString(),
                    total_speaks.toString(),
                    total_lbs.toString(),
                    total_eye.toString(),
                    total.toString(),
                    count.toString()
            );
            rows.add(row);
        }

        List<String> row = CollUtil.newArrayList(
                "总计",
                count1.toString(),
                count2.toString(),
                count3.toString(),
                count4.toString(),
                count5.toString(),
                count0.toString()
        );
        rows.add(row);

        genXls(rows, "功能点使用统计", 20);

    }


    @Test
    public void 统计收款() throws Exception {

        DateTime begin = DateUtil.beginOfDay(new Date());
        DateTime end = DateUtil.endOfDay(new Date());

        String beginStr = DateUtil.format(begin, DatePattern.NORM_DATETIME_PATTERN);
        String endStr = DateUtil.format(end, DatePattern.NORM_DATETIME_PATTERN);

        System.out.println(beginStr);
        System.out.println(endStr);


        String sql = """
                
                SELECT
                	oi.scenic_name,
                	oi.user_name,
                	oi.open_id,
                	oi.create_time,
                	SUM ( oi.order_amount ) amount 
                FROM
                	guide_pay_order_item oi 
                WHERE
                	oi.open_id IS NOT NULL and oi.create_time >= '{beginStr}' and oi.create_time <= '{endStr}'
                GROUP BY
                	oi.open_id,
                	oi.user_name,
                	oi.scenic_name,
                	oi.create_time 
                ORDER BY
                	oi.create_time DESC
                
                """;

        Map<String, Object> param = Maps.newHashMap();
        param.put("beginStr", beginStr);
        param.put("endStr", endStr);

        sql = StrUtil.format(sql, param);

        List<Entity> list = DbUtil.use(ds).query(sql);
//        list = list.stream().filter(item->{
//            return item.getStr("scenic_name").equals(name);
//        }).collect(Collectors.toList());

        List<List<String>> rows = Lists.newArrayList();
        List<String> row0 = CollUtil.newArrayList("景区名称", "用户标识", "收款金额(元)", "收款时间");
        rows.add(row0);

        BigDecimal total = BigDecimal.ZERO;

        for (Entity entity : list) {
            total = total.add(entity.getBigDecimal("amount"));
            String userName = entity.getStr("user_name");
            if (StrUtil.isEmpty(userName)) {
                userName = entity.getStr("open_id");
            }

            List<String> row = CollUtil.newArrayList(
                    entity.getStr("scenic_name"),
                    userName,
                    entity.getStr("amount"),
                    entity.getStr("create_time")
            );
            rows.add(row);
        }
        List<String> row = CollUtil.newArrayList(
                "", "付款人数" + list.size(), "总金额：" + total.toString(), ""
        );
        rows.add(row);

        genXls(rows, "收款统计", 40);

    }


    @Test
    public void 扫码转换率统计() throws Exception {

        DateTime begin = DateUtil.beginOfDay(new Date());
        DateTime end = DateUtil.endOfDay(new Date());

        String dayStr = DateUtil.format(begin, DatePattern.NORM_DATE_PATTERN);
//        String dayStr = "2025-07-05";

        String sql = """
                                    SELECT
                                      t1.*,
                                      CASE
                                        WHEN t2.no_pay_count IS NULL THEN
                                          0
                                        ELSE
                                          t2.no_pay_count
                                      END
                                    FROM
                                      (
                                        SELECT
                                          oi.scenic_id,
                                          oi.scenic_name,
                                          oi.point_id,
                                          oi.point_name,
                                          COUNT(oi.order_statue) pay_count
                                        FROM
                                          guide_pay_order_item oi
                                        WHERE
                                          oi.order_statue IS NOT NULL
                                          AND to_char(oi.insert_time, 'yyyy-MM-dd') = '{dayStr}'
                                        GROUP BY
                                          oi.scenic_id,
                                          oi.scenic_name,
                                          oi.point_id,
                                          oi.point_name
                                        ORDER BY
                                          oi.scenic_id
                                      ) t1
                                      LEFT JOIN (
                                        SELECT
                                          oi.scenic_id,
                                          oi.scenic_name,
                                          oi.point_id,
                                          oi.point_name,
                                          COUNT(*) no_pay_count
                                        FROM
                                          guide_pay_order_item oi
                                        WHERE
                                          oi.order_statue IS NULL
                                          AND to_char(oi.insert_time, 'yyyy-MM-dd') = '{dayStr}'
                                        GROUP BY
                                          oi.scenic_id,
                                          oi.scenic_name,
                                          oi.point_id,
                                          oi.point_name
                                        ORDER BY
                                          oi.scenic_id
                                      ) t2 ON t1.scenic_id = t2.scenic_id
                                      AND t1.point_id = t2.point_id
                """;

        Map<String, Object> param = Maps.newHashMap();
        param.put("dayStr", dayStr);

        sql = StrUtil.format(sql, param);

        List<Entity> list = DbUtil.use(ds).query(sql);

        List<ScenicPointParam> scenicPointParamList = list.stream().map(item -> {
            int scenic_id = item.getInt("scenic_id");
            String scenic_name = item.getStr("scenic_name");
            String point_id = item.getStr("point_id");
            String point_name = item.getStr("point_name");
            int pay_count = item.getInt("pay_count");
            int no_pay_count = item.getInt("no_pay_count");

            ScenicPointParam scenicPointParam = new ScenicPointParam();
            scenicPointParam.setScenicName(scenic_name);
            scenicPointParam.setScenicId(scenic_id);
            scenicPointParam.setPointId(point_id);
            scenicPointParam.setPointName(point_name);
            scenicPointParam.setPayCount(pay_count);
            scenicPointParam.setNoPayCount(no_pay_count);

            return scenicPointParam;
        }).collect(Collectors.toList());

        Map<String, List<ScenicPointParam>> groupMap = HelpMe.groupList(scenicPointParamList, ScenicPointParam::getScenicName);


        List<List<String>> rows = Lists.newArrayList();
        List<String> row0 = CollUtil.newArrayList("景区名称", "点位名称", "扫码人数", "付款人数","未付款人数","转换率");
        rows.add(row0);

        int total = 0;
        int total_pay = 0;
        int total_no_pay = 0;
        for (Map.Entry<String, List<ScenicPointParam>> entry : groupMap.entrySet()) {
            String scenicName = entry.getKey();
            List<ScenicPointParam> scenicPointParamList1 = entry.getValue();

            for (ScenicPointParam scenicPointParam : scenicPointParamList1) {
                total_pay += scenicPointParam.getPayCount();
                total_no_pay += scenicPointParam.getNoPayCount();
                int temp = scenicPointParam.getPayCount() + scenicPointParam.getNoPayCount();
                total += temp;

                BigDecimal dividend = new BigDecimal(scenicPointParam.getPayCount());
                BigDecimal divisor = new BigDecimal(temp);

                // 除法运算，保留2位小数，四舍五入
                BigDecimal result = dividend.divide(divisor, 2, RoundingMode.HALF_UP);

                List<String> row = CollUtil.newArrayList(
                        scenicPointParam.getScenicName(),
                        scenicPointParam.getPointName(),
                        temp+"",
                        scenicPointParam.getPayCount()+"",
                        scenicPointParam.getNoPayCount()+"",
                        result.toString()
                );
                rows.add(row);
            }
        }


        BigDecimal dividend = new BigDecimal(total_pay);
        BigDecimal divisor = new BigDecimal(total);

        // 除法运算，保留2位小数，四舍五入
        BigDecimal result = dividend.divide(divisor, 2, RoundingMode.HALF_UP);

        List<String> row = CollUtil.newArrayList(
                "总计",
                "总计",
                total+"",
                total_pay+"",
                total_no_pay+"",
                result.toString()
        );
        rows.add(row);

        genXls(rows, "扫码转换率统计", 30);
    }

    public static void main(String[] args) {
        BigDecimal dividend = new BigDecimal("3");
        BigDecimal divisor = new BigDecimal("10");

        // 除法运算，保留2位小数，四舍五入
        BigDecimal result = dividend.divide(divisor, 2, RoundingMode.HALF_UP);

        System.out.println("结果: " + result);
    }

    @Test
    public void 注册用户统计_按指定日期() throws Exception {

        String sql = """
                SELECT
                  to_char(dates.date, 'YYYY-MM-DD') AS registration_date,
                  COUNT(u.user_id) AS user_count
                FROM
                  generate_series(
                    '{beginDate}'::date,
                    '{endDate}'::date,
                    '1 day'::interval
                  ) AS dates(date)
                LEFT JOIN sys_user u
                  ON dates.date = u.create_time::date
                GROUP BY dates.date
                ORDER BY dates.date
                """;

        String endDate = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);

        Map<String, String> param = new HashMap<>();
        param.put("beginDate", "2025-04-25");
        param.put("endDate", endDate);

        sql = StrUtil.format(sql, param);

        List<Entity> list = DbUtil.use(ds).query(sql);

        List<List<String>> rows = Lists.newArrayList();
        List<String> row0 = CollUtil.newArrayList("日期", "新增用户");
        rows.add(row0);

        int total = 0;
        for (Entity entity : list) {
            String registration_date = entity.getStr("registration_date");
            int count = entity.getInt("user_count");
            total += count;
            List<String> row = CollUtil.newArrayList(registration_date, count + "");
            rows.add(row);
        }

        sql = "select SUM(amount_incurred) as total from token_detail where change_type = 'IN'";
        //发行游豆数量
        Number issueBeanCount = DbUtil.use(ds).queryNumber(sql);

        List<String> row1 = CollUtil.newArrayList("用户总数：" + total, "游豆总数：" + issueBeanCount);
        rows.add(row1);


        sql = "select SUM(amount_incurred) as total from token_detail where change_type = 'OUT'";
        //用户总使用bean数量
        Number userIssueBeanCount = DbUtil.use(ds).queryNumber(sql);

        //剩余游豆
        int diff = issueBeanCount.intValue() - userIssueBeanCount.intValue();

        List<String> row2 = CollUtil.newArrayList("游豆使用：" + userIssueBeanCount, "游豆剩余：" + diff);
        rows.add(row2);

        genXls(rows, "用户统计", 40);

    }


    @Test
    public void 更新景区缩略图_批量() {
        String path = "/Users/<USER>/Desktop/test/aaa";

        List<File> fileList = FileUtil.loopFiles(path);

        for (File item : fileList) {
            String name = item.getName();
            String extName = FileUtil.extName(item);
            name = StrUtil.removeSuffix(name, "." + extName);

            String sql = "select * from scenic_spot where name = '" + name + "' limit 1";

            try {
                Entity entity = DbUtil.use(ds).queryOne(sql);
                if (entity != null) {

                    String name2 = name + "_缩略图." + extName;

                    String absPath = item.getAbsolutePath();

                    String absPath2 = StrUtil.removeSuffix(absPath, item.getName()) + name2;

                    FileUtil.copy(absPath, absPath2, true);

                    String url = upload2Oss(absPath2);
                    sql = "update scenic_spot set thumbnail = '" + url + "' where name = '" + name + "'";
                    int count = DbUtil.use(ds).execute(sql);
                    log.info("景区名称：{} 更新景区缩略图... {}", name, count);
                } else {
                    log.info("通过景区名称：{} 未查询到数据", name);
                }
            } catch (SQLException e) {
                log.error("执行 sql 失败！", e);
            }
        }

    }


    @Test
    public void test1() {
        String xmlPath = "/Users/<USER>/Desktop/test/a/Annotations/55e040a9-阿坝州.xml";
        String jsonPath = "/Users/<USER>/Desktop/test/output.json";

        System.out.println(123);
    }


    @Test
    public void test2() {

        dealExcel();
    }


    @Test
    public void test3() {
        String txtPath = "/Users/<USER>/Desktop/test/test.txt";
        String failTxtPath = "/Users/<USER>/Desktop/test/fail.txt";
        dealThumbnail(txtPath, failTxtPath);
    }


    @Test
    public void 景区标注ZIP() {
        String zipDir = "/Users/<USER>/Desktop/test";
        List<File> zipList = FileUtil.loopFiles(zipDir);

        zipList = zipList.stream().filter(item -> {
            return FileUtil.extName(item).equalsIgnoreCase("zip");
        }).collect(Collectors.toList());

        for (File zip : zipList) {
            dealZip(zip.getAbsolutePath());
            ThreadUtil.safeSleep(1000);
        }

        String url = "https://ai.ideepyou.com/prod-api/test/user/resetTouristMap";
        HttpUtil.get(url);

    }


    @Test
    public void 当日用户统计() throws Exception {
        String sql = "select count(1) " +
                "   from sys_user " +
                "  where to_char(create_time, 'yyyy-MM-dd') = to_char(now(), 'yyyy-MM-dd')";

        //新增用户数量
        Number todayUserCount = DbUtil.use(ds).queryNumber(sql);

        sql = "select count(1) " +
                "  from sys_user " +
                "  where del_flag = '0' and status = '0'";

        //总用户数量
        Number allUserCount = DbUtil.use(ds).queryNumber(sql);

        sql = "select SUM(amount_incurred) as total from token_detail where change_type = 'IN'";

        //发行游豆数量
        Number issueBeanCount = DbUtil.use(ds).queryNumber(sql);

        sql = "select SUM(amount_incurred) as total from token_detail where change_type = 'OUT'";
        //用户总使用bean数量
        Number userIssueBeanCount = DbUtil.use(ds).queryNumber(sql);

        //剩余游豆
        int diff = issueBeanCount.intValue() - userIssueBeanCount.intValue();


        List<List<String>> rows = Lists.newArrayList();
        List<String> row0 = CollUtil.newArrayList("当天新增用户", "总用户数", "游豆总发行", "游豆总使用", "游豆总剩余");
        rows.add(row0);

        List<String> row = CollUtil.newArrayList(
                todayUserCount.toString(), allUserCount.toString(), issueBeanCount.toString(), userIssueBeanCount.toString(), diff + ""
        );
        rows.add(row);

        genXls(rows, "用户统计", 40);
    }


    private void genXls(List<List<String>> rows, String name, int width) {
        Date now = new Date();
        String str = DateUtil.format(now, DatePattern.PURE_DATE_PATTERN);
        name += "-";
        name += str;

        String excel = "/Users/<USER>/Desktop/temp/" + name + ".xls";

        log.info("导出的文件路径：{}", excel);

        FileUtil.del(excel);

        int size = rows.get(0).size();

        ExcelWriter writer = new ExcelWriter(excel);
        writer.write(rows);

        for (int i = 0; i < size; i++) {
            writer.setColumnWidth(i, width);
        }

        writer.close();
    }


    @Test
    public void test5() {
        String dir = "/Users/<USER>/Desktop/test/tip.gif";

        upload2Oss(dir);
    }


    @Test
    public void 上传缩略图() {
        String name = "明水古城国际泉水旅游度假区";
        String img = "/Users/<USER>/Desktop/test/aaa/明水古城国际泉水旅游度假区_缩略图.jpg";
        String url = upload2Oss(img);

        String sql = "update scenic_spot set thumbnail = '" + url + "' where name = '" + name + "'";

        try {
            int count = DbUtil.use(ds).execute(sql);
            log.info("结果：{}, 执行的 sql : {}", count, sql);
        } catch (SQLException e) {
            log.error("执行 sql 失败！", e);
        }

    }


    @Test
    public void 上传缩略图_批量() {
        String path = "/Users/<USER>/Desktop/test/4A景区缩略图";

        List<File> fileList = FileUtil.loopFiles(path);
        for (File file : fileList) {
            String extName = FileUtil.extName(file);
            String fileName = StrUtil.removeSuffix(file.getName(), "." + extName);

            String url = upload2Oss(file.getAbsolutePath());

            String sql = "update scenic_spot set thumbnail = '" + url + "' where name = '" + fileName + "'";

            try {
                int count = DbUtil.use(ds).execute(sql);
                log.info("结果：{}, 执行的 sql : {}", count, sql);
            } catch (SQLException e) {
                log.error("执行 sql 失败！", e);
            }
        }
    }

    @Test
    public void 上传导览图() {
        String name = "明水古城国际泉水旅游度假区";
        String img = "/Users/<USER>/Desktop/test/明水古城国际泉水旅游度假区.jpg";
        String url = upload2Oss(img);

        String sql = "update scenic_spot set guide_map = '" + url + "' where name = '" + name + "'";

        try {
            int count = DbUtil.use(ds).execute(sql);
            log.info("结果：{}, 执行的 sql : {}", count, sql);
        } catch (SQLException e) {
            log.error("执行 sql 失败！", e);
        }

    }


    @Test
    public void test6() {
        resetTouristLabel("华雅盘石洲森林花园");
    }

    public static void resetTouristLabel(String name) {
        String sql = "update scenic_spot set label_flag = 0 where name = '" + name + "'";
        String sql2 = "delete from sys_tourist_label where tourist_id = (select id from scenic_spot where name = '" + name + "')";

        try {
            int count = DbUtil.use(ds).execute(sql);
            log.info("结果：{}, 执行的 sql : {}", count, sql);

            count = DbUtil.use(ds).execute(sql2);
            log.info("结果：{}, 执行的 sql : {}", count, sql2);
        } catch (SQLException e) {
            log.error("执行 sql 失败！", e);
        }
    }

    public static String upload2Oss(String url, String destFile) {
        long size = HttpUtil.downloadFile(url, destFile);

        String uploadUrl = "https://ai.ideepyou.com/prod-api/guide/user/upFileByHsOss";
        if (size > 0) {
            Map<String, Object> map = new HashMap<>();
            map.put("file", new File(destFile));
            String result = HttpUtil.post(uploadUrl, map);
            log.info("上传结果：{}", result);

            JSONObject obj = JSONUtil.parseObj(result);
            int code = obj.getInt("code");
            if (code == 200) {
                String ossUrl = obj.getStr("data");
                return ossUrl;
            }
        }
        return "";
    }

    public static String upload2Oss(String destFile) {
        String uploadUrl = "https://ai.ideepyou.com/prod-api/guide/user/upFileByHsOss";
        Map<String, Object> map = new HashMap<>();
        map.put("file", new File(destFile));
        String result = HttpUtil.post(uploadUrl, map);
        log.info("上传结果：{}", result);

        JSONObject obj = JSONUtil.parseObj(result);
        int code = obj.getInt("code");
        if (code == 200) {
            String ossUrl = obj.getStr("data");
            return ossUrl;
        }
        return "";
    }


    public static void dealThumbnail(String txtPath, String failTxtPath) {

        List<String> lineList = FileUtil.readUtf8Lines(txtPath);

        for (String item : lineList) {
            JSONObject jsonObject = JSONUtil.parseObj(item);
            String name = jsonObject.getStr("name");
            String url = jsonObject.getStr("url");

            if (url.startsWith("https://space.coze.cn/web")) {
                List<String> tempList = Lists.newArrayList();
                tempList.add(name);
                FileUtil.writeUtf8Lines(tempList, FileUtil.file(failTxtPath));
            } else {
                String destFile = "/Users/<USER>/Desktop/test/" + name + ".jpg";
                String ossUrl = upload2Oss(url, destFile);

                if (StrUtil.isNotEmpty(ossUrl)) {
                    String sql = "update scenic_spot set thumbnail = '" + ossUrl + "' where name = '" + name + "'";
                    try {
                        int count = DbUtil.use(ds).execute(sql);
                        log.info("结果：{}, 执行的 sql : {}", count, sql);
                    } catch (SQLException e) {
                        log.error("执行 sql 失败！", e);
                    }
                }

            }
        }

    }


    public static void dealZip(String zip) {
        File file = ZipUtil.unzip(zip);
        String name = file.getName();

        // 调用方法查找第一个 XML 文件
        String firstXmlPath = findFirstXmlFile(file.getAbsolutePath());

        // 输出结果
        if (firstXmlPath != null) {
            log.info("当前景区名称：{}，xml 文件路径 ：{}", name, firstXmlPath);
        } else {
            log.info("当前景区名称：{},在指定目录及其子目录中未找到 XML 文件", name);
        }

        if (firstXmlPath != null) {
            String jsonPath = "/Users/<USER>/Desktop/test/" + name + ".json";
            dealXml(firstXmlPath, jsonPath);

            String sql = "select id from scenic_spot where label_flag = 0 and name = '" + name + "' limit 1";

            try {
                Entity entity = DbUtil.use(ds).queryOne(sql);
                if (entity != null) {
                    String id = entity.getStr("id");
                    dealJson(id, name, jsonPath);
                } else {
                    log.info("通过名称 {} 及 label_flag 标识，未查询到景区 ID！", name);
                }
            } catch (SQLException e) {
                log.error("{},查询景区ID失败！", name, e);
                throw new RuntimeException(e);
            }
        }


    }

    public static String findFirstXmlFile(String directoryPath) {
        File directory = new File(directoryPath);
        AtomicReference<String> firstXmlPath = new AtomicReference<>(); // 用于存储找到的第一个 XML 文件路径

        // 递归查找文件
        listFiles(directory, firstXmlPath);

        return firstXmlPath.get();
    }

    private static void listFiles(File directory, AtomicReference<String> firstXmlPath) {
        // 如果已经找到文件，停止递归
        if (firstXmlPath.get() != null) {
            return;
        }

        // 如果是文件夹，遍历其中的文件
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    listFiles(file, firstXmlPath); // 递归调用
                }
            }
        } else {
            // 如果是文件，检查是否是 XML 文件
            if (directory.getName().endsWith(".xml")) {
                firstXmlPath.set(directory.getAbsolutePath()); // 存储第一个找到的 XML 文件路径
            }
        }
    }


    public static void dealJson(String tourist_id, String tourist_name, String jsonPath) {
        List<String> lineList = FileUtil.readUtf8Lines(jsonPath);

        String create_time = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);

        String tempSql = "INSERT INTO sys_tourist_label (tourist_id, tourist_name, label_name, image_width, image_height, x1y1, x2y2, create_by, create_time, remark) VALUES " +
                "('{tourist_id}','{tourist_name}', '{label_name}', '{image_width}','{image_height}','{x1y1}','{x2y2}','{create_by}','{create_time}',NULL)";

        Map<String, String> param = Maps.newHashMap();

        param.put("tourist_id", tourist_id);
        param.put("tourist_name", tourist_name);
        param.put("create_time", create_time);
        param.put("create_by", "system");

        int i = 0;
        for (String line : lineList) {
            JSONObject json = JSONUtil.parseObj(line);
            String name = json.getStr("name");
            String xmin = json.getStr("xmin");
            String ymin = json.getStr("ymin");
            String xmax = json.getStr("xmax");
            String ymax = json.getStr("ymax");

            param.put("label_name", name);
            param.put("image_width", "0");
            param.put("image_height", "0");
            param.put("x1y1", xmin + "," + ymin);
            param.put("x2y2", xmax + "," + ymax);


            String sql = StrUtil.format(tempSql, param);
            try {
                int count = DbUtil.use(ds).execute(sql);
                log.info("当前第 {} 个，执行 sql : {}", (++i), sql);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }

        }

        String sql = "update scenic_spot set label_flag = 1 where id = " + tourist_id;

        try {
            int count = DbUtil.use(ds).execute(sql);
            log.info("更新景区 label_flag 标识，执行 sql : {}", sql);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

    }

    public static void dealExcel() {
        ExcelReader reader = ExcelUtil.getReader("/Users/<USER>/Desktop/test/labels_my-project-name_2025-04-09-03-19-27.xlsx");
        List<List<Object>> readAll = reader.read();
        reader.close();

        String tourist_id = "21";
        String tourist_name = "故宫";
        String create_time = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);

        String tempSql = "INSERT INTO ai-guide.sys_tourist_label (tourist_id, tourist_name, label_name, image_width, image_height, x1y1, x2y2, create_by, create_time, remark) VALUES " +
                "('{tourist_id}','{tourist_name}', '{label_name}', '{image_width}','{image_height}','{x1y1}','{x2y2}','{create_by}','{create_time}',NULL)";

        Map<String, String> param = Maps.newHashMap();

        param.put("tourist_id", tourist_id);
        param.put("tourist_name", tourist_name);
        param.put("create_time", create_time);
        param.put("create_by", "system");

        for (int i = 0; i < readAll.size(); i++) {
            if (i == 0) continue;
            List<Object> temp = readAll.get(i);
            String label_name = temp.get(0).toString();
            String x1 = temp.get(1).toString();
            String y1 = temp.get(2).toString();

            String temp1 = temp.get(3).toString();
            String temp2 = temp.get(4).toString();

            int x2 = Integer.parseInt(x1) + Integer.parseInt(temp1);
            int y2 = Integer.parseInt(y1) + Integer.parseInt(temp2);

            String image_width = temp.get(6).toString();
            String image_height = temp.get(7).toString();

            param.put("label_name", label_name);
            param.put("image_width", image_width);
            param.put("image_height", image_height);
            param.put("x1y1", x1 + "," + y1);
            param.put("x2y2", x2 + "," + y2);


            String sql = StrUtil.format(tempSql, param);
            try {
                int count = DbUtil.use(ds).execute(sql);
                log.info("当前第 {} 个，执行 sql : {}", (i + 1), sql);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }

    }


    public static void insertTxt() {
        String tourist_id = "2";
        String tourist_name = "恭王府";
        String create_time = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);

        String tempSql = "insert into sys_tourist_txt(tourist_id,tourist_name,title,content,create_time) values('{tourist_id}','{tourist_name}','{title}','{content}','{create_time}')";

        Map<String, String> param = Maps.newHashMap();

        param.put("tourist_id", tourist_id);
        param.put("tourist_name", tourist_name);
        param.put("create_time", create_time);

        String path = "/Users/<USER>/Desktop/temp/data.txt";

        JSONArray arr = JSONUtil.parseArray(FileUtil.readUtf8String(path));

        for (int i = 0; i < arr.size(); i++) {
            Object data = arr.get(i);
            JSONObject json = JSONUtil.parseObj(data);
            String title = json.getStr("title");
            String content = json.getStr("content");
            param.put("title", title);
            param.put("content", content);

            String sql = StrUtil.format(tempSql, param);
            try {
                int count = DbUtil.use(ds).execute(sql);
                log.info("当前第 {} 个，执行 sql : {}", (i + 1), sql);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }

    }


    public static void insertPhoto() {
        String tourist_id = "2";
        String tourist_name = "恭王府";
        String create_time = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);

        String tempSql = "insert into sys_tourist_photo(tourist_id,tourist_name,url,create_time) values('{tourist_id}','{tourist_name}','{url}','{create_time}')";

        Map<String, String> param = Maps.newHashMap();

        param.put("tourist_id", tourist_id);
        param.put("tourist_name", tourist_name);
        param.put("create_time", create_time);

        String path = "/Users/<USER>/Desktop/temp/urlData.txt";

        List<String> lineList = FileUtil.readUtf8Lines(path);

        for (int i = 0; i < lineList.size(); i++) {
            param.put("url", lineList.get(i));
            String sql = StrUtil.format(tempSql, param);
            try {
                int count = DbUtil.use(ds).execute(sql);
                log.info("当前第 {} 个，执行 sql : {}", (i + 1), sql);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }

    }

    public static void dealFile() {
        String dir = "/Users/<USER>/Desktop/test2/pic";

        List<File> list = FileUtil.loopFiles(dir);
        List<File> delList = Lists.newArrayList();

        for (File file : list) {
            if (file.getName().contains("hd")) {
            } else {
                FileUtil.del(file);
            }
        }
    }

    public static void query() {

        List<Entity> list = Lists.newArrayList();

        try {
            list = DbUtil.use(ds).query("select * from sys_config");
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }


        log.info("数据：{}", list);

    }


    public static void dealXml(String xmlPath, String jsonPath) {
        try {
            // 输入输出文件路径
            File xmlFile = new File(xmlPath);
            File outputFile = new File(jsonPath);

            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(xmlFile);
            doc.getDocumentElement().normalize();

            NodeList objectList = doc.getElementsByTagName("object");
            List<String> jsonLines = new ArrayList<>();

            // 处理每个object节点
            for (int i = 0; i < objectList.getLength(); i++) {
                Element object = (Element) objectList.item(i);
                jsonLines.add(convertToSimplifiedJson(object));
            }

            // 写入文件
            try (FileWriter writer = new FileWriter(outputFile)) {
                for (String json : jsonLines) {
                    writer.write(json + "\n");
                }
            }

            log.info("转换完成，输出文件: " + outputFile.getAbsolutePath());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String convertToSimplifiedJson(Element object) {
        Element bndbox = (Element) object.getElementsByTagName("bndbox").item(0);

        return String.format(
                "{" +
                        "\"name\": \"%s\"," +  // 名称保持字符串类型
                        "\"xmin\": %s," +       // 坐标转为数值类型
                        "\"ymin\": %s," +
                        "\"xmax\": %s," +
                        "\"ymax\": %s" +
                        "}",
                getCleanValue("name", object),
                getNumericValue("xmin", bndbox),
                getNumericValue("ymin", bndbox),
                getNumericValue("xmax", bndbox),
                getNumericValue("ymax", bndbox)
        );
    }

    // 获取文本内容（自动去空格）
    private static String getCleanValue(String tag, Element element) {
        NodeList nodeList = element.getElementsByTagName(tag);
        return nodeList.item(0).getTextContent().trim();
    }

    // 专门处理数值字段
    private static String getNumericValue(String tag, Element element) {
        String value = getCleanValue(tag, element);
        // 添加数字校验（可选）
        if (!value.matches("\\d+")) {
            System.err.println("非数值内容: " + tag + " = " + value);
        }
        return value;
    }


}
