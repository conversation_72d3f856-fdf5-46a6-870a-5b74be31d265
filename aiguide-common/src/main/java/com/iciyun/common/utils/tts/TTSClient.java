package com.iciyun.common.utils.tts;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.iciyun.common.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

@Slf4j
@SuppressWarnings("ALL")
public class TTSClient {
    private static final String SAVE_PATH = "/Users/<USER>/Desktop/test";
    private static final OkHttpClient CLIENT;

    private String apiUrl;

    private void setUrl(){
        String url = "http://*************:8006/tts";
        if (Constants.isPro){
            url = "https://tts.ideepyou.com/tts";
        }
        apiUrl = url;
    }

    // 单例模式确保 client 只初始化一次
    static {
        CLIENT = new OkHttpClient.Builder()
                .connectTimeout(2, TimeUnit.MINUTES)
                .readTimeout(2, TimeUnit.MINUTES)
                .writeTimeout(2, TimeUnit.MINUTES)
                .retryOnConnectionFailure(true)
                .connectionPool(new ConnectionPool(500, 30, TimeUnit.MINUTES))
                .dns(new Dns() {
                    private final Map<String, List<InetAddress>> cache = new ConcurrentHashMap<>();

                    @NotNull
                    @Override
                    public List<InetAddress> lookup(String hostname) throws UnknownHostException {
                        return cache.computeIfAbsent(hostname, h -> {
                            try {
                                return Dns.SYSTEM.lookup(hostname);
                            } catch (UnknownHostException e) {
                                throw new RuntimeException(e);
                            }
                        });
                    }
                })
                .build();
    }

    public TTSClient() {
        setUrl();
    }
    public TTSClient(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public void synthesizeAndSave(String text, ConcurrentHashMap<String, Long> stats, CountDownLatch latch) throws IOException {
        String encodedText = HttpUrl.parse(apiUrl).newBuilder()
                .addQueryParameter("text", text)
                .addQueryParameter("cut_punc", "按英文句号.切")
                .addQueryParameter("text_language", "中文")
                .build().toString();

        Request request = new Request.Builder()
                .url(encodedText)
                .build();

        long startTime = System.currentTimeMillis(); // 记录请求开始时间

        CLIENT.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                System.err.println("请求失败: " + e.getMessage());
                stats.put("failure", stats.getOrDefault("failure", 0L) + 1);
                latch.countDown(); // 每个请求完成后递减计数器
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                long endTime = System.currentTimeMillis(); // 记录请求结束时间
                long duration = endTime - startTime; // 计算请求耗时

                try {
                    if (!response.isSuccessful()) {
                        throw new IOException("Unexpected code: " + response);
                    }

                    // 确保输出目录存在
                    File outputDir = new File(SAVE_PATH).getParentFile();
                    if (!outputDir.exists()) {
                        synchronized (TTSClient.class) { // 同步代码块确保线程安全
                            if (!outputDir.exists()) {
                                outputDir.mkdirs();
                            }
                        }
                    }

                    String str = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
                    String audioFile = IdUtil.fastSimpleUUID() + "_" + str + ".wav";
                    String fullPath = SAVE_PATH + "/" + audioFile;

                    InputStream input = null;
                    OutputStream output = null;
                    try {
                        input = response.body().byteStream();
                        output = Files.newOutputStream(Path.of(fullPath));
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = input.read(buffer)) != -1) {
                            output.write(buffer, 0, bytesRead);
                        }
                        output.flush();
                        System.out.println("音频已保存至: " + fullPath);
                        stats.put("success", stats.getOrDefault("success", 0L) + 1);
                        stats.put("totalTime", stats.getOrDefault("totalTime", 0L) + duration);
                        stats.put("responseTimes", stats.getOrDefault("responseTimes", 0L) + 1);

                        // 按时间段统计响应时间分布
                        String timeRange = getResponseTimeRange(duration);
                        stats.put(timeRange, stats.getOrDefault(timeRange, 0L) + 1);
                    } finally {
                        IoUtil.close(input);
                        IoUtil.close(output);
                    }
                } catch (Exception e) {
                    stats.put("failure", stats.getOrDefault("failure", 0L) + 1);
                    e.printStackTrace();
                } finally {
                    latch.countDown(); // 每个请求完成后递减计数器
                }
            }
        });
    }


    public String synthesizeAndSave(String voiceId, String text,String speed) throws IOException {
        String encodedText = HttpUrl.parse(apiUrl).newBuilder()
                .addQueryParameter("voiceId", voiceId)
                .addQueryParameter("text", text)
                .addQueryParameter("speed", speed)
                .addQueryParameter("cut_punc", "按英文句号.切")
                .addQueryParameter("text_language", "中英混合")
                .build().toString();

        Request request = new Request.Builder()
                .url(encodedText)
                .build();

        long startTime = System.currentTimeMillis(); // 记录请求开始时间

        try (Response response = CLIENT.newCall(request).execute()) { // 使用同步调用

            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code: " + response);
            }

            // 确保输出目录存在
            File outputDir = new File(SAVE_PATH).getParentFile();
            if (!outputDir.exists()) {
                synchronized (TTSClient.class) { // 同步代码块确保线程安全
                    if (!outputDir.exists()) {
                        outputDir.mkdirs();
                    }
                }
            }

            String str = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
            String audioFile = IdUtil.fastSimpleUUID() + "_" + str + ".wav";
            String fullPath = SAVE_PATH + "/" + audioFile;

            InputStream input = null;
            OutputStream output = null;
            try {
                input = response.body().byteStream();
                output = Files.newOutputStream(Path.of(fullPath));
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = input.read(buffer)) != -1) {
                    output.write(buffer, 0, bytesRead);
                }
                output.flush();
                System.out.println("音频已保存至: " + fullPath);
                return fullPath;
            } finally {
                IoUtil.close(input);
                IoUtil.close(output);
            }
        } catch (IOException e) {
            throw e; // 抛出异常以便上层捕获
        }
    }


    public String ttsWithWavPath(String voiceId, String text,String speed,String wavPath,String textLanguage) throws IOException {
        if (StrUtil.isEmpty(textLanguage)){
            textLanguage = "中英混合";
        }
        String encodedText = HttpUrl.parse(apiUrl).newBuilder()
                .addQueryParameter("voiceId", voiceId)
                .addQueryParameter("text", text)
                .addQueryParameter("speed", speed)
                .addQueryParameter("cut_punc", "按英文句号.切")
                .addQueryParameter("text_language", textLanguage)
                .build().toString();

        Request request = new Request.Builder()
                .url(encodedText)
                .build();

        long startTime = System.currentTimeMillis(); // 记录请求开始时间

        try (Response response = CLIENT.newCall(request).execute()) { // 使用同步调用

            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code: " + response);
            }

            // 确保输出目录存在
            File outputDir = new File(SAVE_PATH).getParentFile();
            if (!outputDir.exists()) {
                synchronized (TTSClient.class) { // 同步代码块确保线程安全
                    if (!outputDir.exists()) {
                        outputDir.mkdirs();
                    }
                }
            }


            InputStream input = null;
            OutputStream output = null;
            try {
                input = response.body().byteStream();
                output = Files.newOutputStream(Path.of(wavPath));
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = input.read(buffer)) != -1) {
                    output.write(buffer, 0, bytesRead);
                }
                output.flush();
                System.out.println("音频已保存至: " + wavPath);
                return wavPath;
            } finally {
                IoUtil.close(input);
                IoUtil.close(output);
            }
        } catch (IOException e) {
            throw e; // 抛出异常以便上层捕获
        }
    }


    /**
     * 可传参数：
     * voiceId: 音色ID
     * text: 文本内容
     * text_language: 语言类型
     *     - 中文
     *     - 粤语
     *     - 英文
     *     - 日文
     *     - 韩文
     *     - 中英混合
     *     - 粤英混合
     *     - 日英混合
     *     - 韩英混合
     * cut_punc: 文本切分（太长的文本合成出的效果并不好，建议先切，合成会根据文本的换行分开合成再拼起来）
     *     - 不切
     *     - 凑四句一切
     *     - 凑50字一切
     *     - 按中文句号。切
     *     - 按英文句号.切
     *     - 按标点符号切
     * speed: 文本语速控制
     *
     * @param voiceId
     * @param text
     * @param speed
     * @return
     * @throws IOException
     */
    public static final String 中英混合 = "中英混合";
    public static final String 粤语 = "粤语";
    public static final String 日文 = "日文";
    public static final String 英文 = "英文";
    public byte[] GPT_SoVITS(String voiceId, String text,String speed,String textLanguage) throws IOException {
        if (StrUtil.isEmpty(textLanguage)){
            textLanguage = "中英混合";
        }
        String encodedText = HttpUrl.parse(apiUrl).newBuilder()
                .addQueryParameter("voiceId", voiceId)
                .addQueryParameter("text", text)
                .addQueryParameter("speed", speed)
                .addQueryParameter("cut_punc", "按英文句号.切")
                .addQueryParameter("text_language", textLanguage)
                .build().toString();

        Request request = new Request.Builder()
                .url(encodedText)
                .build();

        try (Response response = CLIENT.newCall(request).execute()) { // 使用同步调用
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code: " + response);
            }
            return response.body().byteStream().readAllBytes();
        } catch (IOException e) {
            throw e; // 抛出异常以便上层捕获
        }
    }


    public void tts(String voiceId, String text,String fileName,String speed) throws IOException {
        String encodedText = HttpUrl.parse(apiUrl).newBuilder()
                .addQueryParameter("voiceId", voiceId)
                .addQueryParameter("text", text)
                .addQueryParameter("speed", speed)
                .addQueryParameter("cut_punc", "按英文句号.切")
                .addQueryParameter("text_language", "中文")
                .build().toString();

        Request request = new Request.Builder()
                .url(encodedText)
                .build();

        try (Response response = CLIENT.newCall(request).execute()) { // 使用同步调用
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code: " + response);
            }

            InputStream input = null;
            OutputStream output = null;
            try {
                input = response.body().byteStream();
                output = Files.newOutputStream(Path.of(fileName));
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = input.read(buffer)) != -1) {
                    output.write(buffer, 0, bytesRead);
                }
                output.flush();
                log.info("音频已保存至: " + fileName);
            } finally {
                IoUtil.close(input);
                IoUtil.close(output);
            }
        } catch (IOException e) {
            throw e; // 抛出异常以便上层捕获
        }
    }


    private String getResponseTimeRange(long duration) {
        if (duration < 100) return "<100ms";
        else if (duration < 200) return "100-200ms";
        else if (duration < 500) return "200-500ms";
        else if (duration < 1000) return "500-1000ms";
        else if (duration < 2000) return "1000-2000ms";
        else return ">2000ms";
    }

    public static void main1(String[] args) {
        TTSClient tts = new TTSClient();
        ConcurrentHashMap<String, Long> stats = new ConcurrentHashMap<>();
        CountDownLatch latch = new CountDownLatch(1); // 示例中只执行一次请求
        try {
            tts.synthesizeAndSave("你好，欢迎来到北京，我是你的导游小明，很高兴为你服务。", stats, latch);
            latch.await(); // 等待请求完成
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    public static void main2(String[] args) throws Exception {
        TTSClient tts = new TTSClient("https://tts.ideepyou.com/tts");
//        TTSClient tts = new TTSClient("http://*************:8006/tts");

        long startTime = System.currentTimeMillis(); // 记录请求开始时间

        String text = """
                
                ここが広利門（こうりもん）で、天壇西側の塀にある主要な入口の一つであり、深い礼制文化の意味が込められています。「広利」の名称は『書経』の「広く天下を利する」に由来し、五穀豊穣と万民の幸福を祈る意味があります。赤い壁と緑の瓦を持つこの宮門は三連アーチ構造で、中央の御道は皇帝の祭祀専用、両側の門は官吏用であり、この厳格な階級規制は古代の「天を敬い祖に法る」という礼制思想の生きた体現です。
                """;

        String fullPath = "/Users/<USER>/Desktop/test/test.wav";

        String voiceId = "7426725529589661723";
//        String voiceId = "7426725529589645339";
//        String path = tts.synthesizeAndSave(voiceId, text,"1.0");
        tts.ttsWithWavPath(voiceId, text,"0.9",fullPath,TTSClient.日文);

        long endTime = System.currentTimeMillis(); // 记录请求开始时间

        System.out.println("请求耗时: " + (endTime - startTime) + "ms path: " + fullPath);
    }


    public static void main(String[] args) throws Exception {
        TTSClient tts = new TTSClient("https://tts.ideepyou.com/tts");
//        TTSClient tts = new TTSClient("http://*************:8006/tts");

        String path = "/Users/<USER>/Desktop/project/aiguide-back/doc/自定义音色.json";
        String json = FileUtil.readString(path, Charset.forName("UTF-8"));
        JSONArray arr = JSONUtil.parseArray(json);

        String text = """
                
               欢迎来到明翠楼！
                
                """;

        for (int i = 0; i < arr.size(); i++) {
            JSONObject one = JSONUtil.parseObj(arr.get(i));
            String voice_id = one.getStr("voice_id");
            String type = one.getStr("type");
            String fullPath = "/Users/<USER>/Desktop/test/"+type+".wav";

            long startTime = System.currentTimeMillis(); // 记录请求开始时间

            tts.ttsWithWavPath(voice_id, text,"1",fullPath,TTSClient.中英混合);

            long endTime = System.currentTimeMillis(); // 记录请求开始时间

            System.out.println("请求耗时: " + (endTime - startTime) + "ms path: " + fullPath);
            ThreadUtil.safeSleep(1000);
        }


    }


}