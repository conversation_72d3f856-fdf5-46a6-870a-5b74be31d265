package com.iciyun.common.utils.tts;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.iciyun.common.constant.Constants;
import com.iciyun.common.utils.HelpMe;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

@Slf4j
@SuppressWarnings("ALL")
public class TTSClient {
    private static final String SAVE_PATH = "/Users/<USER>/Desktop/test";
    private static final OkHttpClient CLIENT;

    private String apiUrl;

    private void setUrl(){
        String url = "http://*************:8006/tts";
        if (Constants.isPro){
            url = "https://tts.ideepyou.com/tts";
        }
        apiUrl = url;
    }

    // 单例模式确保 client 只初始化一次
    static {
        CLIENT = new OkHttpClient.Builder()
                .connectTimeout(2, TimeUnit.MINUTES)
                .readTimeout(2, TimeUnit.MINUTES)
                .writeTimeout(2, TimeUnit.MINUTES)
                .retryOnConnectionFailure(true)
                .connectionPool(new ConnectionPool(500, 30, TimeUnit.MINUTES))
                .dns(new Dns() {
                    private final Map<String, List<InetAddress>> cache = new ConcurrentHashMap<>();

                    @NotNull
                    @Override
                    public List<InetAddress> lookup(String hostname) throws UnknownHostException {
                        return cache.computeIfAbsent(hostname, h -> {
                            try {
                                return Dns.SYSTEM.lookup(hostname);
                            } catch (UnknownHostException e) {
                                throw new RuntimeException(e);
                            }
                        });
                    }
                })
                .build();
    }

    public TTSClient() {
        setUrl();
    }
    public TTSClient(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public void synthesizeAndSave(String text, ConcurrentHashMap<String, Long> stats, CountDownLatch latch) throws IOException {
        String encodedText = HttpUrl.parse(apiUrl).newBuilder()
                .addQueryParameter("text", text)
                .addQueryParameter("cut_punc", "按英文句号.切")
                .addQueryParameter("text_language", "中文")
                .build().toString();

        Request request = new Request.Builder()
                .url(encodedText)
                .build();

        long startTime = System.currentTimeMillis(); // 记录请求开始时间

        CLIENT.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                System.err.println("请求失败: " + e.getMessage());
                stats.put("failure", stats.getOrDefault("failure", 0L) + 1);
                latch.countDown(); // 每个请求完成后递减计数器
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                long endTime = System.currentTimeMillis(); // 记录请求结束时间
                long duration = endTime - startTime; // 计算请求耗时

                try {
                    if (!response.isSuccessful()) {
                        throw new IOException("Unexpected code: " + response);
                    }

                    // 确保输出目录存在
                    File outputDir = new File(SAVE_PATH).getParentFile();
                    if (!outputDir.exists()) {
                        synchronized (TTSClient.class) { // 同步代码块确保线程安全
                            if (!outputDir.exists()) {
                                outputDir.mkdirs();
                            }
                        }
                    }

                    String str = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
                    String audioFile = IdUtil.fastSimpleUUID() + "_" + str + ".wav";
                    String fullPath = SAVE_PATH + "/" + audioFile;

                    InputStream input = null;
                    OutputStream output = null;
                    try {
                        input = response.body().byteStream();
                        output = Files.newOutputStream(Path.of(fullPath));
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = input.read(buffer)) != -1) {
                            output.write(buffer, 0, bytesRead);
                        }
                        output.flush();
                        System.out.println("音频已保存至: " + fullPath);
                        stats.put("success", stats.getOrDefault("success", 0L) + 1);
                        stats.put("totalTime", stats.getOrDefault("totalTime", 0L) + duration);
                        stats.put("responseTimes", stats.getOrDefault("responseTimes", 0L) + 1);

                        // 按时间段统计响应时间分布
                        String timeRange = getResponseTimeRange(duration);
                        stats.put(timeRange, stats.getOrDefault(timeRange, 0L) + 1);
                    } finally {
                        IoUtil.close(input);
                        IoUtil.close(output);
                    }
                } catch (Exception e) {
                    stats.put("failure", stats.getOrDefault("failure", 0L) + 1);
                    e.printStackTrace();
                } finally {
                    latch.countDown(); // 每个请求完成后递减计数器
                }
            }
        });
    }


    public String synthesizeAndSave(String voiceId, String text,String speed) throws IOException {
        String encodedText = HttpUrl.parse(apiUrl).newBuilder()
                .addQueryParameter("voiceId", voiceId)
                .addQueryParameter("text", text)
                .addQueryParameter("speed", speed)
                .addQueryParameter("cut_punc", "按英文句号.切")
                .addQueryParameter("text_language", "中英混合")
                .build().toString();

        Request request = new Request.Builder()
                .url(encodedText)
                .build();

        long startTime = System.currentTimeMillis(); // 记录请求开始时间

        try (Response response = CLIENT.newCall(request).execute()) { // 使用同步调用

            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code: " + response);
            }

            // 确保输出目录存在
            File outputDir = new File(SAVE_PATH).getParentFile();
            if (!outputDir.exists()) {
                synchronized (TTSClient.class) { // 同步代码块确保线程安全
                    if (!outputDir.exists()) {
                        outputDir.mkdirs();
                    }
                }
            }

            String str = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
            String audioFile = IdUtil.fastSimpleUUID() + "_" + str + ".wav";
            String fullPath = SAVE_PATH + "/" + audioFile;

            InputStream input = null;
            OutputStream output = null;
            try {
                input = response.body().byteStream();
                output = Files.newOutputStream(Path.of(fullPath));
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = input.read(buffer)) != -1) {
                    output.write(buffer, 0, bytesRead);
                }
                output.flush();
                System.out.println("音频已保存至: " + fullPath);
                return fullPath;
            } finally {
                IoUtil.close(input);
                IoUtil.close(output);
            }
        } catch (IOException e) {
            throw e; // 抛出异常以便上层捕获
        }
    }


    public String ttsWithWavPath(String voiceId, String text,String speed,String wavPath,String textLanguage) throws IOException {
        if (StrUtil.isEmpty(textLanguage)){
            textLanguage = "中英混合";
        }
        String encodedText = HttpUrl.parse(apiUrl).newBuilder()
                .addQueryParameter("voiceId", voiceId)
                .addQueryParameter("text", text)
                .addQueryParameter("speed", speed)
                .addQueryParameter("cut_punc", "按英文句号.切")
                .addQueryParameter("text_language", textLanguage)
                .build().toString();

        Request request = new Request.Builder()
                .url(encodedText)
                .build();

        long startTime = System.currentTimeMillis(); // 记录请求开始时间

        try (Response response = CLIENT.newCall(request).execute()) { // 使用同步调用

            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code: " + response);
            }

            // 确保输出目录存在
            File outputDir = new File(SAVE_PATH).getParentFile();
            if (!outputDir.exists()) {
                synchronized (TTSClient.class) { // 同步代码块确保线程安全
                    if (!outputDir.exists()) {
                        outputDir.mkdirs();
                    }
                }
            }


            InputStream input = null;
            OutputStream output = null;
            try {
                input = response.body().byteStream();
                output = Files.newOutputStream(Path.of(wavPath));
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = input.read(buffer)) != -1) {
                    output.write(buffer, 0, bytesRead);
                }
                output.flush();
                System.out.println("音频已保存至: " + wavPath);
                return wavPath;
            } finally {
                IoUtil.close(input);
                IoUtil.close(output);
            }
        } catch (IOException e) {
            throw e; // 抛出异常以便上层捕获
        }
    }


    /**
     * 可传参数：
     * voiceId: 音色ID
     * text: 文本内容
     * text_language: 语言类型
     *     - 中文
     *     - 粤语
     *     - 英文
     *     - 日文
     *     - 韩文
     *     - 中英混合
     *     - 粤英混合
     *     - 日英混合
     *     - 韩英混合
     * cut_punc: 文本切分（太长的文本合成出的效果并不好，建议先切，合成会根据文本的换行分开合成再拼起来）
     *     - 不切
     *     - 凑四句一切
     *     - 凑50字一切
     *     - 按中文句号。切
     *     - 按英文句号.切
     *     - 按标点符号切
     * speed: 文本语速控制
     *
     * @param voiceId
     * @param text
     * @param speed
     * @return
     * @throws IOException
     */
    public static final String 中英混合 = "中英混合";
    public static final String 粤语 = "粤语";
    public static final String 日文 = "日文";
    public static final String 英文 = "英文";
    public byte[] GPT_SoVITS(String voiceId, String text,String speed,String textLanguage) throws IOException {
        if (StrUtil.isEmpty(textLanguage)){
            textLanguage = "中英混合";
        }
        String encodedText = HttpUrl.parse(apiUrl).newBuilder()
                .addQueryParameter("voiceId", voiceId)
                .addQueryParameter("text", text)
                .addQueryParameter("speed", speed)
                .addQueryParameter("cut_punc", "按英文句号.切")
                .addQueryParameter("text_language", textLanguage)
                .build().toString();

        Request request = new Request.Builder()
                .url(encodedText)
                .build();

        try (Response response = CLIENT.newCall(request).execute()) { // 使用同步调用
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code: " + response);
            }
            return response.body().byteStream().readAllBytes();
        } catch (IOException e) {
            throw e; // 抛出异常以便上层捕获
        }
    }


    public void tts(String voiceId, String text,String fileName,String speed) throws IOException {
        String encodedText = HttpUrl.parse(apiUrl).newBuilder()
                .addQueryParameter("voiceId", voiceId)
                .addQueryParameter("text", text)
                .addQueryParameter("speed", speed)
                .addQueryParameter("cut_punc", "按英文句号.切")
                .addQueryParameter("text_language", "中文")
                .build().toString();

        Request request = new Request.Builder()
                .url(encodedText)
                .build();

        try (Response response = CLIENT.newCall(request).execute()) { // 使用同步调用
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code: " + response);
            }

            InputStream input = null;
            OutputStream output = null;
            try {
                input = response.body().byteStream();
                output = Files.newOutputStream(Path.of(fileName));
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = input.read(buffer)) != -1) {
                    output.write(buffer, 0, bytesRead);
                }
                output.flush();
                log.info("音频已保存至: " + fileName);
            } finally {
                IoUtil.close(input);
                IoUtil.close(output);
            }
        } catch (IOException e) {
            throw e; // 抛出异常以便上层捕获
        }
    }


    private String getResponseTimeRange(long duration) {
        if (duration < 100) return "<100ms";
        else if (duration < 200) return "100-200ms";
        else if (duration < 500) return "200-500ms";
        else if (duration < 1000) return "500-1000ms";
        else if (duration < 2000) return "1000-2000ms";
        else return ">2000ms";
    }

    public static void main1(String[] args) {
        TTSClient tts = new TTSClient();
        ConcurrentHashMap<String, Long> stats = new ConcurrentHashMap<>();
        CountDownLatch latch = new CountDownLatch(1); // 示例中只执行一次请求
        try {
            tts.synthesizeAndSave("你好，欢迎来到北京，我是你的导游小明，很高兴为你服务。", stats, latch);
            latch.await(); // 等待请求完成
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    public static void main2(String[] args) throws Exception {
        TTSClient tts = new TTSClient("https://tts.ideepyou.com/tts");
//        TTSClient tts = new TTSClient("http://*************:8006/tts");

        long startTime = System.currentTimeMillis(); // 记录请求开始时间

        String text = """
                
                ここが広利門（こうりもん）で、天壇西側の塀にある主要な入口の一つであり、深い礼制文化の意味が込められています。「広利」の名称は『書経』の「広く天下を利する」に由来し、五穀豊穣と万民の幸福を祈る意味があります。赤い壁と緑の瓦を持つこの宮門は三連アーチ構造で、中央の御道は皇帝の祭祀専用、両側の門は官吏用であり、この厳格な階級規制は古代の「天を敬い祖に法る」という礼制思想の生きた体現です。
                """;

        String fullPath = "/Users/<USER>/Desktop/test/test.wav";

        String voiceId = "7426725529589661723";
//        String voiceId = "7426725529589645339";
//        String path = tts.synthesizeAndSave(voiceId, text,"1.0");
        tts.ttsWithWavPath(voiceId, text,"0.9",fullPath,TTSClient.日文);

        long endTime = System.currentTimeMillis(); // 记录请求开始时间

        System.out.println("请求耗时: " + (endTime - startTime) + "ms path: " + fullPath);
    }

    public static void main(String[] args) throws Exception{

        String content = """
                
                这边所在的这一栋建筑物是以北方典型的“二进式四合院”的形式展现的，2012年重修，整栋建筑占地1200平方，参观核心1000平方左右，杨家埠木板年画主要是以民俗为主，分为了四个区域分别是：福、禄、寿、喜。
                首先进入年画博物馆，映入眼前的就是一副长达20米的画乡风情，讲述了杨家埠的一些重要节日。
                动木日：二月初六为杨家埠的动木日，因为杨家埠印年画需要先制板，制板必动木、动木必锯材，也取聚财的谐音。
                植槐日：三月十六为植槐日，以前杨家埠几乎都会在家家户户门口栽种槐树，因为以前印年画的主色调黄色、绿色都是从槐树中提取的。杨家埠也流传着这样一句话：“门前一棵槐，财源滚滚来”槐树也是杨家埠的发家之本。
                拜师节：五月十二为杨家埠的拜师节。村中年满九岁的孩童都由家族族长带领到杨氏宗祠集中拜师。先向年画始祖杨伯达行三拜九叩之礼，再向本家年画高手行拜师礼。
                槐神节：六月初六，杨家埠人从祖辈起就把槐树敬奉为神，所以就在明代永乐三年（1408年）定为每年农历的六月六为槐神节。
                婚俗三宝：明清时期女儿出嫁必带娘家赠送的三张宝画压在出嫁的箱子底下，即“张仙射狗、麒麟送子、春宫图”也被称为中国最早期的性教育图。
                财神节：七月二十二，杨家埠当地过得比较隆重的节日，北方相较于南方要过得更为隆重，像过年一样，七月二十二也俗称为“财神爷爷”过生日。
                启行典礼：八月二十，从前农历的八月二十就种植完小麦了，农民们就开始着手印制年画。
                开庄日：九月初九，在外开店的杨家埠人都会由家族族长的带领下来到杨氏宗祠来向老祖宗辞行，行三拜九叩大礼，祈祷先祖保佑儿孙在外开庄顺利，兴旺发达，然后到外地开设分店，扩大经营。
                熬黄日：九月二十六，过去杨家埠有一个不成文的规定，无论你家的店面有多大，需要多少的黄色必须要在这一天统一熬制黄色，为统一把握质量关。
                挂福字灯：十月十三，这一天所有的杨家埠画店都会在这一天挂上福字灯，挂福字灯的寓意以求，画市兴旺，画业发达。
                犒劳案子：十月二十五，这天晚上主人会大摆筵席犒劳伙计就叫做犒劳案子，目的只有一个就是让伙计好好干活。
                止案子：腊月初八，这一天一律撤掉案子，停止印画，打扫庭院，置办年货，准备过年。
                选举子：腊月初十，为“推选举子”日，在这一天会在自己的店里挑选一张绘印年画俱佳的年画精品，带到杨氏宗祠，由画行行长和族长负责挑选推选上来的年画选出一张颜色鲜艳、构图饱满的年画，推选为年画举子。这一张年画将会在下一年卖得非常好。
                留古画：腊月十八，印了一冬卖了一年的年画，此时店主会静下心来挑选出一张绘印都很好的年画保存下来，以求有一天能成为“活画”
                庄会：腊月二十，在外开店的杨家埠人会纷纷关闭店门回家过年。
                辞灶：腊月二十三，也就是俗称的小年，在这一天会在灶台上贴上大灶王，会把糖瓜作为供品，希望灶王能够甜言蜜语，上天言好事，下界降吉祥。
                从面前的这道门开始正式进入博物馆主体部分，馆内特设置了“福禄寿喜”门，面前所经过的呢就是第一道门：“福”字门，在福字门的下方，当您张开双臂便可以形成一个田字取意“人在福中走，福字心中留”愿您福与天大。
                过来之后可以看到上方的这一幅年画是由我们的杨氏先祖杨伯达亲手绘制定稿的一幅年画，三代宗亲以此思乡敬祖，共分为三层，第一层祖影，第二层故灵神位，第三层自家大门。下面这一块木板是一块反板的大灶王，所选用的木材就都是水果梨的木头，这边是西杨家埠村151家画店的传承表，都是由明代开始传承的，像西杨家埠村共有三百多户人家就有151家画店，也就是说这个村有一半乃至一半以上的人都在从事风筝和年画相关的产业。
                在往边上看是由我们杨氏先祖杨伯达所创的“同顺堂”的传习谱，已经到了第二十世“杨乃东”（非物质文化遗产传承人）
                这一幅水浒一百单八将是由现代艺术家严克臣，严师傅历时一年多刻制完成的木板拓印出来的。由四块板拼接而成，是杨家埠史上篇幅最长，人物最多的年画。
                天下十八省：我们这边看到的这张旧痕斑斑的年画就是我们杨家埠木板年画博物馆的镇馆之宝-天下十八省，它始创于明代，清光绪乙酉年重修，是以马跑的速度测算两地之间的距离加以天数计算，最后以指南针定位，由六块木板拼接印制而成，地图上东北三省的地区比较模糊，是因为在明代东三省隶属满族属于满洲里。这就是中国最早的商用地图。
                门上所贴的这对门神是我们中国最早的临街门神“神荼、郁垒”始于上古时期有着镇宅、驱邪、保平安之作。中国的门神可粗分为三大类：武门神、文门神、祈福门神；到了后来，五花八门新增加的门神取代了神荼、郁垒等先秦门神，门神的含意、形式和内容随着历史的延续发生了一系列的变化。后期新增的门神主要有“祈福类”、“道界类”、“文官类”、“武将类”及“其他杂类”等等。祈福类如赐福天官，道界类如钟馗与王灵官，文官类魏征、包公和文天祥，武将类如秦琼与尉迟恭等等
                所经过面前的这道门叫做“禄”字门，外形像铜钱一样，寓意“禄禄高升”。杨家埠的风筝分为六大类，而我们的年画则分为五大类。
                第一类：神像类，就以我们刚才所说的门神，神荼、郁垒为代表作，但是我们现在张贴门神的就非常少了，都会用到下边的这张大灶王，每年的腊月二十三也就是小年我们都会在灶台上贴上灶王，以糖瓜供果为供品，希望灶王能上天言好事，下界降吉祥。灶王又称灶君、灶神、灶王爷。
                第二类：神话传说类，以底下的摇钱树聚宝盆为代表作，通常贴在炕头之上，也俗称为“炕头画”，寓意金钱怎么摇也摇不完，源源不断的钱财。
                第三类：童子类，《十子争梅》、《桂阁产麒麟》是贴在新婚儿媳和长辈的房间内，寓意早生贵子、多子多孙。
                第四类：美人条，杨家埠年画的美人条类以其色彩艳丽、构图精美备受闺秀所钟爱，同时也是我们中国最早的门房画的一种，中国的古风是很封建的，女士闺房是不可以随意进出，贴在指定闺房门口起到警示的作用，禁止男士入内。
                第五类：花卉类，以春牡丹、夏荷花、秋菊花、冬梅花的博古四条屏为代表作，多贴于书房之内，杨家埠的年画最少是需要4种颜色最多7种颜色组成的，但是这一组年画单张就需要12块板分上下两个部分，分别套制而成。
                第三道门：“寿”字门，寿字迎头照，愿您寿比山高。旁边案子上像您展示的是明清时期印制年画时所需要的部分工具，从颜料器皿到印画案子，斗转星移，光阴荏苒，这些明代的印画器具大多成了文物，成了历史的见证。正前方是我们杨家埠所获得的部分奖杯和证书，在2006年的时候杨家埠的风筝和年画同时被国务院列入首批国家级非物质文化遗产，成功申请双非遗，也是成功获批的首批非物质文化遗产，这些文件和证书，也记载了杨家埠年画的非凡历程。两边所看到的这些年画展品都是复制品，真品都被国外的博物馆和上海图书馆所收藏。
                所经过的最后一道门：“喜”字门，照壁上有一个喜字，寓意“出门见喜，愿您喜事多多”。我们两边所看到陈列的这些雕版都是杨家埠版藏珍品，每一块都是时代的缩影，每一块都在像您讲述着“老百姓的故事”都是以边上的这颗梨木的木材刻制而成的，梨木的木材木质坚硬、细腻、光滑，遇水不易腐烂变形，像在最早的时候我们都是选用棠栗木，但是棠栗木生长极其缓慢现在非常的珍贵稀少，所以我们用梨木的木材来代替棠梨木。
                增福财神：面前的是杨家埠最大的一块可印刷木版，高2.4米，宽1.5米，版幅达到了3.6平方米的反版“增福财神”，在2004年年初由杨家埠艺人，组成的研发小组，历时一年多反复修订，精雕细琢，一幅造型精美、构图饱满、刀法细腻、线条清晰的反版“增福财神”它是由八颗梨木的木材来拼接刻制而成的，也是现存版幅最大的一块可印刷木版。
                接下来，我们来到的是年画作坊，作坊由三个部分组成，一是年画雕版，二是年画印制，三是年画制作互动体验区。我们先来参观刻版的制作过程，像我们这边所看到的就是刻版的制作过程，师傅现在面前所摆置的版材就都是我们吃的水果梨的梨木木材，梨木的木材木质坚硬、细腻、横竖设有丝条是年画雕版的上佳原料，师傅会先绘制好一个底稿，然后粘贴在版面上之后会顺着纹路进行雕刻，师傅刻制的版分为两种：一种是正版，是用来观赏和收藏的；另一种，则是反版，用来印刷年画的，正版只需要刻制一块木板而反版则需要多块色板拼成一幅完整的年画，这边所看的这些师傅都是国家级非物质文化遗产传承人同时也是省级工艺美工大师。
                这边所要参观的是杨家埠年画印染区，这边所看到的制作过程所选用的是传承了600多年的传统套色印刷的手法来进行制作的，纸张都是选用了安徽的一级生宣纸，师傅在印制年画时会先用黑色的木版来印出图案的轮廓，然后再逐一套色印刷，只有版面凸起的地方会沾染到纸上，其他的地方就都是凹下去的，像一会师傅印制完这一种颜色之后就会换另一块木板来进行印制，换板的时候纸是固定不动的，“动板不动纸”，师傅会先用手摸摸凸起来的地方是否在黑色图案轮廓里，如果不在的话师傅就用旁边的小锤轻轻敲击，予以调整位置。我们杨家埠的木版年画最少需要四块木板，最多则需要七块木板来分别印制而成的，看着简单但实际操作起来非常难，因为版都是不固定的，上色的时候位置非常容易偏移掉，再有就是换版的时候位置是完全没有固定的点，要放在哪里全凭师傅多年的制作经验。要求印出来的年画“画面干巴巴，还要密花花，四处不沾是好画”，没有三年的功底是做不到的，后边架子上撘着的就都是印制好的成品年画。来杨家埠，请一个灶王和门神年画回去几乎成了游客来杨家埠的首选。
                此外，还专门设置了单色和套色的年画印刷体验区，您可以亲身体验一下年画印制的传统技艺，当一回守艺人。前面是年画文创区，杨家埠将木版年画的元素进行延展，与卡通样式相结合，推了各种样式的文创产品，像杨家埠的文创雪糕、冰箱贴、钥匙挂件、摆台、帆布包等款式多样，无论是精致实用的伴手礼，还是承载文化的艺术品，这里总有一款能让你一眼心动，带走一份独一无二的非遗记忆！
                
                """;

        TTSClient tts = new TTSClient("https://tts.ideepyou.com/tts");

        if (content.length()>1600){
            List<String> strList = HelpMe.splitStringByLength(content, 1600);

            int i = 0;
            for (String str : strList) {
                i++;
                long startTime = System.currentTimeMillis(); // 记录请求开始时间

                String fullPath = "/Users/<USER>/Desktop/test/test_"+i+".wav";

                String voiceId = "7426725529589661723";

                tts.ttsWithWavPath(voiceId, str,"1.0",fullPath,TTSClient.中英混合);

                long endTime = System.currentTimeMillis(); // 记录请求开始时间

                System.out.println("请求耗时: " + (endTime - startTime) + "ms path: " + fullPath);
            }

        }

    }

    public static void main3(String[] args) throws Exception {
        TTSClient tts = new TTSClient("https://tts.ideepyou.com/tts");
//        TTSClient tts = new TTSClient("http://*************:8006/tts");

        String path = "/Users/<USER>/Desktop/project/aiguide-back/doc/自定义音色.json";
        String json = FileUtil.readString(path, Charset.forName("UTF-8"));
        JSONArray arr = JSONUtil.parseArray(json);

        String text = """
                
               欢迎来到明翠楼！
                
                """;

        for (int i = 0; i < arr.size(); i++) {
            JSONObject one = JSONUtil.parseObj(arr.get(i));
            String voice_id = one.getStr("voice_id");
            String type = one.getStr("type");
            String fullPath = "/Users/<USER>/Desktop/test/"+type+".wav";

            long startTime = System.currentTimeMillis(); // 记录请求开始时间

            tts.ttsWithWavPath(voice_id, text,"1",fullPath,TTSClient.中英混合);

            long endTime = System.currentTimeMillis(); // 记录请求开始时间

            System.out.println("请求耗时: " + (endTime - startTime) + "ms path: " + fullPath);
            ThreadUtil.safeSleep(1000);
        }


    }


}