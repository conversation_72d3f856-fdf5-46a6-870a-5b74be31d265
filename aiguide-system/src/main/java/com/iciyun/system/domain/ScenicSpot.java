package com.iciyun.system.domain;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import com.iciyun.common.constant.Constants;
import com.iciyun.common.enums.ScenicSpotStatusEnum;
import com.iciyun.system.domain.bo.*;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02 14:44:00
 */
@Getter
@Setter
@TableName("scenic_spot")
@NoArgsConstructor
public class ScenicSpot implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final Integer ZOOM_DEFAULT = 16;
    private static final Integer LENGTH_DEFAULT = 16;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    //    解说词生成标识   0 未生成   1 已生成
    @TableField("cache_ok")
    private Integer cacheOk;

    //    音频缓存标识  0 未缓存完成   1 已缓存完成
    @TableField("audio_cache_ok")
    private Integer audioCacheOk;

    /**
     * 景区 ID
     */
    @TableField("scenic_spot_id")
    private String scenicSpotId;

//    景区简介
    @TableField("label_text")
    private String labelText;


    /**
     * 景区名称
     */
    @TableField("name")
    private String name;

    /**
     * 风格和语言，json 格式：
     *
     * {
     *   "style": [
     *     "CULTURE"
     *   ],
     *   "language": [
     *     "Chinese",
     *     "English"
     *   ]
     * }
     *
     */
    @TableField("style_and_language")
    private String styleAndLanguage;

    /**
     * 风格和语言开关  0 关闭  1 开启
     */
    @TableField("style_and_language_switch")
    private Integer styleAndLanguageSwitch;

    /**
     * 0 不允许AI 生成内容   1 允许AI 生成内容
     */
    @TableField("aigc_switch")
    private Integer aigcSwitch;

    /**
     * 省份编码
     */
    @TableField("province_code")
    private String provinceCode;

    /**
     * 省份名称
     */
    @TableField("province_name")
    private String provinceName;

    /**
     * 城市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 城市名称
     */
    @TableField("city_name")
    private String cityName;

    /**
     * 区县编码
     */
    @TableField("district_code")
    private String districtCode;

    /**
     * 区县名称
     */
    @TableField("district_name")
    private String districtName;

    /**
     * 详细地址
     */
    @TableField("detail_address")
    private String detailAddress;

    /**
     * 负责人
     */
    @TableField("kahuna")
    private String kahuna;

    /**
     * 联系电话
     */
    @TableField("contact_number")
    private String contactNumber;

    /**
     * 上线状态
     */
    @TableField("status")
    private String status;

    /**
     * 导览图
     */
    @TableField("guide_map")
    private String guideMap;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    /**
     * 景区纬度
     */
    @TableField("latitude")
    private String latitude;

    /**
     * 景区经度
     */
    @TableField("longitude")
    private String longitude;
    /**
     * 景区级别
     */
    @TableField("level")
    private Integer level;

    /**
     * 游豆消耗数量
     */
    @TableField("need_token")
    private BigDecimal needToken;


    /**
     * 0 未标注   1 已标注
     */
    @TableField("label_flag")
    private Integer labelFlag;

    /**
     * 距离
     */
    @TableField(exist = false)
    private String distance;

    /**
     * 缩略图
     */
    @TableField("thumbnail")
    private String thumbnail;

    /**
     * 图层
     */
    @TableField("zoom")
    private Integer zoom;

    /**
     * Ai导游服务费
     */
    @TableField("service_charge")
    private BigDecimal serviceCharge;

    /**
     * 围栏圆形半径（米）
     */
    @TableField("radius")
    private Integer radius = 20;

    /**
     * 是否重复讲解（0：否；1：是）
     */
    @TableField("repetition")
    private Integer repetition;

    /**
     * 复讲间隔时间（秒）
     */
    @TableField("interval_time")
    private Integer intervalTime;

    /**
     * 更新腾讯地图坐标状态
     */
    @TableField("tmap_status")
    private Integer tmapStatus;

    /**
     * 腾讯地图景区名称
     */
    @TableField("tmap_scenic_name")
    private String tmapScenicName;

    /**
     * 更新腾讯地图坐标状态
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("check_time")
    private LocalDateTime checkTime;

    /**
     *  是否显示地图（0：否；1：是）
     */
    @TableField("tmap_show")
    private Integer tmapShow;

    /**
     *  开开眼优先识别（1：文化符号；2：动植物）
     */
    @TableField("identify_type")
    private Integer identifyType;

    /**
     * 腾讯地图POIID
     */
    @TableField("poi_id")
    private String poiId;

    /**
     * 腾讯地图景区经纬度点串结果
     */
    @TableField("polygon")
    private String polygon;

    /**
     * 腾讯地图点聚合距离
     * 单位像素
     * 默认50
     */
    @TableField("grid_size")
    private Integer gridSize;

    /**
     * 腾讯地图查询关键词
     */
    @TableField("keyword")
    private String keyword;

    /**
     * 腾讯地图查询筛选条件
     */
    @TableField("filter")
    private String filter;

    /**
     * Ai导游服务活动价
     */
    @TableField("service_activity")
    private BigDecimal serviceActivity;

    /**
     * Ai导游服务活动价 -- 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("activity_begin_time")
    private LocalDateTime activityBeginTime;

    /**
     * Ai导游服务活动价 -- 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("activity_end_time")
    private LocalDateTime activityEndTime;


    /**
     * 景区类型：ScenicTypeEnum
     */
    @TableField("scenic_type")
    private Integer scenicType;

    /** 是否合作 */
    @TableField(exist = false)
    private boolean cooperate;

    /**
     * POI 分类
     */
    @TableField("category")
    private String category;

    /**
     * POI 分类编码
     */
    @TableField("category_code")
    private Integer categoryCode;

    /**
     * 开开眼显示默认距离
     */
    @TableField("open_eye_distance")
    private String openEyeDistance;

    /** 景区景点 */
    @TableField(exist = false)
    private List<SysTouristLabel> SysTouristLabels;

    /** 是否有景区数据采集员 */
    @TableField(exist = false)
    private boolean scenicInfoFlag;

    /**
     * 开开眼状态：true：启动，false：关闭
     *
     */
    @TableField("open_eye_status")
    private boolean openEyeStatus;

    /**
     * 景区游玩路线
     */
    @TableField("tourist_routes_raw")
    private String touristRoutesRaw;

    /**
     * 景区游玩路线
     */
    @TableField(exist = false)
    private List<TouristRoute> touristRoutes;


    /**
     * 景区讲解包
     */
    @TableField("tourist_packages_raw")
    private String touristPackagesRaw;

    /**
     * 景区讲解包
     */
    @TableField(exist = false)
    private List<TouristPackage> touristPackages;



    public String getLongitude() {
        return StringUtils.isNotBlank(longitude) ? longitude.trim() : StringUtils.EMPTY;
    }

    public String getLatitude() {
        return StringUtils.isNotBlank(latitude) ? latitude.trim() : StringUtils.EMPTY;
    }

    public double getLongitudeDouble() {
        return StringUtils.isNotBlank(longitude) ? Double.parseDouble(longitude) : 0;
    }

    public double getLatitudeDouble() {
        return StringUtils.isNotBlank(latitude) ? Double.parseDouble(latitude) : 0;
    }


    public ScenicSpot(ScenicSpotAddCmd cmd) {
        this.name = cmd.getName();
        this.provinceName = cmd.getProvinceName();
        this.cityName = cmd.getCityName();
        this.districtName = cmd.getDistrictName();
        this.provinceCode = cmd.getProvinceCode();
        this.cityCode = cmd.getCityCode();
        this.districtCode = cmd.getDistrictCode();
        this.detailAddress = cmd.getDetailAddress();
        this.longitude = StringUtils.left(cmd.getLatitude(), LENGTH_DEFAULT);
        this.latitude = StringUtils.left(cmd.getLongitude(), LENGTH_DEFAULT);
        this.status = cmd.getStatus();
        this.kahuna = cmd.getKahuna();
        this.contactNumber = cmd.getContactNumber();
        this.level = cmd.getLevel();
        this.polygon = cmd.getPolygon();
        this.guideMap = cmd.getGuideMap();
        this.thumbnail = cmd.getThumbnail();
        if (StrUtil.isEmpty(cmd.getStyleAndLanguage())) {
            this.styleAndLanguage = """
                          {
                            "style": [
                              "CULTURE"
                            ],
                            "language": [
                              "Chinese"
                            ]
                          }
                    """;
        }else {
            this.styleAndLanguage = cmd.getStyleAndLanguage();
        }
        this.styleAndLanguageSwitch = cmd.getStyleAndLanguageSwitch();
        this.aigcSwitch = cmd.getAigcSwitch();
        this.zoom = cmd.getZoom();
        this.filter = Constants.FILTER_DEFAULT;
        this.init();
    }

    public ScenicSpot(ScenicSpotImportCmd cmd) {
        this.name = cmd.getName();
        this.level = cmd.getLevel();
        this.provinceName = cmd.getProvinceName();
        this.cityName = cmd.getCityName();
        this.status = ScenicSpotStatusEnum.ON.getCode();
        this.keyword = cmd.getKeyword();
        this.filter = Constants.FILTER_DEFAULT;

        this.styleAndLanguage = """
                      {
                        "style": [
                          "CULTURE"
                        ],
                        "language": [
                          "Chinese"
                        ]
                      }
                """;

        this.styleAndLanguageSwitch = 0;
        this.aigcSwitch = 1;

        this.zoom = ZOOM_DEFAULT;
        this.category = Constants.CATEGORY_DEFAULT;
        this.categoryCode = Constants.CATEGORY_CODE_DEFAULT;
        this.gridSize = Constants.GRID_SIZE_DEFAULT;

        this.init();
    }



    public void init() {
        this.scenicSpotId = IdUtil.getSnowflakeNextIdStr();
        this.keyword = this.name;
        this.tmapScenicName = this.name;

        Date now = new Date();
        this.createTime = now;
        this.updateTime = now;
        this.tmapShow = 1;
    }

    public void edit(ScenicSpotEditCmd cmd) {

        this.name = cmd.getName();
        this.provinceName = cmd.getProvinceName();
        this.cityName = cmd.getCityName();
        this.districtName = cmd.getDistrictName();
        this.provinceCode = cmd.getProvinceCode();
        this.cityCode = cmd.getCityCode();
        this.districtCode = cmd.getDistrictCode();
        this.detailAddress = cmd.getDetailAddress();
        this.longitude = StringUtils.left(cmd.getLatitude(), LENGTH_DEFAULT);
        this.latitude = StringUtils.left(cmd.getLongitude(), LENGTH_DEFAULT);
        this.status = cmd.getStatus();
        this.kahuna = cmd.getKahuna();
        this.contactNumber = cmd.getContactNumber();
        this.level = cmd.getLevel();
        this.polygon = cmd.getPolygon();
        this.guideMap = cmd.getGuideMap();
        this.thumbnail = cmd.getThumbnail();
        this.styleAndLanguage = cmd.getStyleAndLanguage();
        this.updateTime = new Date();
        this.styleAndLanguageSwitch = cmd.getStyleAndLanguageSwitch();
        this.aigcSwitch = cmd.getAigcSwitch();
        this.zoom = cmd.getZoom();
    }

    public String getAddress() {
        return provinceName + cityName + districtName + detailAddress;
    }

    public void supplementDistrictCode(String provinceCode, String cityCode, String districtCode) {
        this.provinceCode = provinceCode;
        this.cityCode = cityCode;
        this.districtCode = districtCode;
    }

    public void checkEdit() {
        this.checkTime = LocalDateTime.now();
        this.updateTime = new Date();
    }

    public void setMapDefaultValue() {
        this.zoom = ZOOM_DEFAULT;
        this.category = Constants.CATEGORY_DEFAULT;
        this.categoryCode = Constants.CATEGORY_CODE_DEFAULT;
        this.gridSize = Constants.GRID_SIZE_DEFAULT;
        this.filter = Constants.FILTER_DEFAULT;
    }

    public void  supplementMapInfo(SupplementMapInfoCmd cmd){
        this.provinceName = cmd.getProvinceName();
        this.cityName = cmd.getCityName();
        this.districtName = cmd.getDistrictName();
        this.provinceCode = cmd.getProvinceCode();
        this.cityCode = cmd.getCityCode();
        this.districtCode = cmd.getDistrictCode();
        this.longitude = StringUtils.left(cmd.getLatitude(), LENGTH_DEFAULT);
        this.latitude = StringUtils.left(cmd.getLongitude(), LENGTH_DEFAULT);
        this.category = cmd.getCategory();
        this.categoryCode = cmd.getCategoryCode();
        this.tmapScenicName = cmd.getTmapScenicName();
        this.poiId = cmd.getPoiId();
        this.name = cmd.getName();
        if (StringUtils.isBlank(this.keyword)) {
            this.keyword = cmd.getKeyword();
        }
        if (StringUtils.isBlank(this.detailAddress)) {
            this.detailAddress = cmd.getDetailAddress();
        }

    }
//
//    public List<String> getPolygonList() {
//        return StringUtils.isBlank(this.polygon) ? List.of() : Arrays.asList(this.polygon.split(";"));
//    }
//
//    public void setPolygonList(List<String> polygonList) {
//        this.polygon = String.join(";", polygonList);
//    }

    public TouristRoute addTouristRoute(TouristRouteAddCmd cmd) {

        if (Objects.isNull(this.touristRoutesRaw) || this.touristRoutesRaw.trim().isEmpty()) {
            this.touristRoutes = new ArrayList<>();
        } else {
            this.touristRoutes = JSONArray.parseArray(this.touristRoutesRaw, TouristRoute.class);
        }
        TouristRoute touristRoute = new TouristRoute(cmd);
        this.touristRoutes.add(new TouristRoute(cmd));
        this.touristRoutesRaw = JSONArray.toJSONString(this.touristRoutes);

        return touristRoute;
    }

    public void editTouristRoute(TouristRouteEditCmd cmd) {

        this.touristRoutes = JSONArray.parseArray(this.touristRoutesRaw, TouristRoute.class);
        this.touristRoutes.stream().filter(tr -> tr.getTouristRouteId().equals(cmd.getTouristRouteId()))
                .findFirst().ifPresent(tr -> {
                    tr.edit(cmd);
                });
        this.touristRoutesRaw = JSONArray.toJSONString(this.touristRoutes);
    }


    public void delTouristRoute(TouristRouteDelCmd cmd) {

        this.touristRoutes = JSONArray.parseArray(this.touristRoutesRaw, TouristRoute.class);
        this.touristRoutes.removeIf(tr -> tr.getTouristRouteId().equals(cmd.getTouristRouteId()));
        this.touristRoutesRaw = JSONArray.toJSONString(this.touristRoutes);
    }

    public TouristRoute.TouristRouteLabel saveTouristRouteLabel(TouristRouteLabelSaveCmd cmd) {

        AtomicReference<TouristRoute.TouristRouteLabel> touristRouteLabel = new AtomicReference<>();

        this.touristRoutes = JSONArray.parseArray(this.touristRoutesRaw, TouristRoute.class);

        this.touristRoutes.stream().filter(tr -> tr.getTouristRouteId().equals(cmd.getTouristRouteId()))
                .findFirst().ifPresent(tr -> {

                    touristRouteLabel.set(tr.saveTouristRouteLabel(cmd));

                });

        this.touristRoutesRaw = JSONArray.toJSONString(this.touristRoutes);

        return touristRouteLabel.get();

    }

    public void editTouristRouteLabel(TouristRouteLabelEditCmd cmd) {

        this.touristRoutes = JSONArray.parseArray(this.touristRoutesRaw, TouristRoute.class);
        this.touristRoutes.stream().filter(tr -> tr.getTouristRouteId().equals(cmd.getTouristRouteId()))
                .findFirst().ifPresent(tr -> {
                    tr.editTouristRouteLabel(cmd);
                });
        this.touristRoutesRaw = JSONArray.toJSONString(this.touristRoutes);

    }

    public void delTouristRouteLabel(TouristRouteLabelDelCmd cmd) {

        this.touristRoutes = JSONArray.parseArray(this.touristRoutesRaw, TouristRoute.class);
        this.touristRoutes.stream().filter(tr -> tr.getTouristRouteId().equals(cmd.getTouristRouteId()))
                .findFirst().ifPresent(tr -> {
                    tr.delTouristRouteLabel(cmd);
                });
        this.touristRoutesRaw = JSONArray.toJSONString(this.touristRoutes);

    }

    public void sortTouristRouteLabel(TouristRouteLabelSortCmd cmd) {
        this.touristRoutes = JSONArray.parseArray(this.touristRoutesRaw, TouristRoute.class);
        this.touristRoutes.stream().filter(tr -> tr.getTouristRouteId().equals(cmd.getTouristRouteId()))
                .findFirst().ifPresent(tr -> {
                    tr.sortTouristRouteLabel(cmd);
                });
        this.touristRoutesRaw = JSONArray.toJSONString(this.touristRoutes);
    }

    public TouristPackage.TouristPackageLabel saveTouristPackageLabel(TouristPackageLabelSaveCmd cmd) {

        if (Objects.isNull(this.touristPackagesRaw) || this.touristPackagesRaw.trim().isEmpty()) {
            this.touristPackages = new ArrayList<>();
        } else {
            this.touristPackages = JSONArray.parseArray(this.touristPackagesRaw, TouristPackage.class);
        }

        TouristPackage touristPackage = this.touristPackages.stream().findFirst().orElseGet(() -> new TouristPackage(cmd));
        TouristPackage.TouristPackageLabel touristPackageLabel = touristPackage.saveTouristPackageLabel(cmd);
        List<TouristPackage> touristPackages = new ArrayList<>();
        touristPackages.add(touristPackage);

        this.touristPackages = touristPackages;
        this.touristPackagesRaw = JSONArray.toJSONString(this.touristPackages);

        return touristPackageLabel;
    }

    public void editTouristPackageLabel(TouristPackageLabelEditCmd cmd) {

        this.touristPackages = JSONArray.parseArray(this.touristPackagesRaw, TouristPackage.class);
        this.touristPackages.stream()
                .findFirst()
                .ifPresent(tp -> tp.editTouristPackageLabel(cmd));
        this.touristPackagesRaw = JSONArray.toJSONString(this.touristPackages);
    }

    public void changeTryListenStatus(TouristPackageLabelTryListenChangeCmd cmd) {
        this.touristPackages = JSONArray.parseArray(this.touristPackagesRaw, TouristPackage.class);
        this.touristPackages.stream()
                .findFirst()
                .ifPresent(tp -> tp.changeTryListenStatus(cmd));
        this.touristPackagesRaw = JSONArray.toJSONString(this.touristPackages);
    }

    public void delTouristPackageLabel(TouristPackageLabelDelCmd cmd) {
        this.touristPackages = JSONArray.parseArray(this.touristPackagesRaw, TouristPackage.class);
        this.touristPackages.stream()
                .findFirst()
                .ifPresent(tp -> tp.delTouristPackageLabel(cmd));
        this.touristPackagesRaw = JSONArray.toJSONString(this.touristPackages);
    }

    public void sortTouristPackageLabel(TouristPackageLabelSortCmd cmd) {
        this.touristPackages = JSONArray.parseArray(this.touristPackagesRaw, TouristPackage.class);
        this.touristPackages.stream()
                .findFirst()
                .ifPresent(tp -> tp.sortTouristPackageLabel(cmd));
        this.touristPackagesRaw = JSONArray.toJSONString(this.touristPackages);
    }

    public List<TouristPackage> getTouristPackages() {
        if (Objects.isNull(this.touristPackagesRaw) || this.touristPackagesRaw.trim().isEmpty()) {
            this.touristPackages = new ArrayList<>();
        } else {
            this.touristPackages = JSONArray.parseArray(this.touristPackagesRaw, TouristPackage.class);
        }
        return this.touristPackages;
    }

    public List<TouristRoute> getTouristRoutes() {

        if (Objects.isNull(this.touristRoutesRaw) || this.touristRoutesRaw.trim().isEmpty()) {
            this.touristRoutes = new ArrayList<>();
        } else {
            this.touristRoutes = JSONArray.parseArray(this.touristRoutesRaw, TouristRoute.class);
        }

        return touristRoutes;

    }

    public BigDecimal handleServiceActivity() {

        if (this.activityBeginTime == null || this.activityEndTime == null) {
            if( this.serviceActivity  == null){
                // 没有设置活动价，使用服务价
                this.serviceActivity = this.serviceCharge;
            }
        } else {
            LocalDate now = LocalDate.now();
            LocalDate start = this.activityBeginTime.toLocalDate();
            LocalDate end = this.activityEndTime.toLocalDate();
            if (now.compareTo(start) >= 0 &&
                    now.compareTo(end) <= 0) {
                if( this.serviceActivity  == null){
                    // 没有设置活动价，使用服务价
                    this.serviceActivity = this.serviceCharge;
                }
            } else {
                this.serviceActivity = this.serviceCharge;
            }
        }
        return serviceActivity;
    }

    public static void main(String[] args) {
        ScenicSpot scenicSpot = new ScenicSpot();
        scenicSpot.setStyleAndLanguage("""
                {
                  "style": [
                    "CULTURE"
                  ],
                  "language": [
                    "Chinese",
                    "English"
                  ]
                }
                """);
        StyleAndLanguage styleAndLanguage = scenicSpot.parseStyleAndLanguage();
        System.out.println(styleAndLanguage);
    }

    public StyleAndLanguage parseStyleAndLanguage(){
        StyleAndLanguage bean = new StyleAndLanguage();
        try {
            JSONObject obj = JSONUtil.parseObj(this.styleAndLanguage);
            bean = JSONUtil.toBean(obj, StyleAndLanguage.class);
        }catch (Exception e){}
        return bean;
    }

    @Data
    public static class StyleAndLanguage{
        private List<String> style = Lists.newArrayList();
        private List<String> language = Lists.newArrayList();
    }

}
