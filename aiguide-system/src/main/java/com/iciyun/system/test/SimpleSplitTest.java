import java.util.*;

/**
 * 简化版的字符串分割测试，只包含我们需要的方法
 */
public class SimpleSplitTest {
    
    /**
     * 对给定的字符串，按指定的长度分割成 n 个小字符串，按分割的顺序添加到 List 中返回
     *
     * @param str    要分割的字符串
     * @param length 分割的长度
     * @return 分割后的字符串列表
     */
    public static List<String> splitStringByLength(String str, int length) {
        List<String> result = new ArrayList<>();
        
        // 参数校验
        if (isNull(str)) {
            return result;
        }
        
        if (length <= 0) {
            throw new IllegalArgumentException("分割长度必须大于0");
        }
        
        // 如果字符串长度小于等于分割长度，直接返回原字符串
        if (str.length() <= length) {
            result.add(str);
            return result;
        }
        
        // 按指定长度分割字符串
        int startIndex = 0;
        while (startIndex < str.length()) {
            int endIndex = Math.min(startIndex + length, str.length());
            String substring = str.substring(startIndex, endIndex);
            result.add(substring);
            startIndex = endIndex;
        }
        
        return result;
    }
    
    // 简化的 isNull 方法
    public static boolean isNull(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    public static void main(String[] args) {
        System.out.println("=== 字符串分割方法测试 ===\n");
        
        // 测试用例1：正常分割
        System.out.println("测试用例1：正常分割");
        String str1 = "HelloWorldJavaSpring";
        int length1 = 5;
        List<String> result1 = splitStringByLength(str1, length1);
        System.out.println("原字符串: " + str1);
        System.out.println("分割长度: " + length1);
        System.out.println("分割结果: " + result1);
        System.out.println("分割后的子字符串个数: " + result1.size());
        System.out.println();
        
        // 测试用例2：字符串长度小于分割长度
        System.out.println("测试用例2：字符串长度小于分割长度");
        String str2 = "Java";
        int length2 = 10;
        List<String> result2 = splitStringByLength(str2, length2);
        System.out.println("原字符串: " + str2);
        System.out.println("分割长度: " + length2);
        System.out.println("分割结果: " + result2);
        System.out.println();
        
        // 测试用例3：中文字符串分割
        System.out.println("测试用例3：中文字符串分割");
        String str3 = "这是一个测试中文字符串分割的例子";
        int length3 = 6;
        List<String> result3 = splitStringByLength(str3, length3);
        System.out.println("原字符串: " + str3);
        System.out.println("分割长度: " + length3);
        System.out.println("分割结果: " + result3);
        System.out.println();
        
        // 测试用例4：空字符串
        System.out.println("测试用例4：空字符串");
        String str4 = "";
        int length4 = 3;
        List<String> result4 = splitStringByLength(str4, length4);
        System.out.println("原字符串: \"" + str4 + "\"");
        System.out.println("分割长度: " + length4);
        System.out.println("分割结果: " + result4);
        System.out.println("结果是否为空列表: " + result4.isEmpty());
        System.out.println();
        
        // 测试用例5：null字符串
        System.out.println("测试用例5：null字符串");
        String str5 = null;
        int length5 = 3;
        List<String> result5 = splitStringByLength(str5, length5);
        System.out.println("原字符串: " + str5);
        System.out.println("分割长度: " + length5);
        System.out.println("分割结果: " + result5);
        System.out.println("结果是否为空列表: " + result5.isEmpty());
        System.out.println();
        
        // 测试用例6：分割长度为1
        System.out.println("测试用例6：分割长度为1");
        String str6 = "ABCDE";
        int length6 = 1;
        List<String> result6 = splitStringByLength(str6, length6);
        System.out.println("原字符串: " + str6);
        System.out.println("分割长度: " + length6);
        System.out.println("分割结果: " + result6);
        System.out.println();
        
        // 测试用例7：数字字符串分割
        System.out.println("测试用例7：数字字符串分割");
        String str7 = "1234567890123456";
        int length7 = 4;
        List<String> result7 = splitStringByLength(str7, length7);
        System.out.println("原字符串: " + str7);
        System.out.println("分割长度: " + length7);
        System.out.println("分割结果: " + result7);
        System.out.println();
        
        // 测试异常情况：分割长度为0
        System.out.println("测试异常情况：分割长度为0");
        try {
            String str8 = "Test";
            int length8 = 0;
            List<String> result8 = splitStringByLength(str8, length8);
        } catch (IllegalArgumentException e) {
            System.out.println("捕获到预期异常: " + e.getMessage());
        }
        System.out.println();
        
        // 测试异常情况：分割长度为负数
        System.out.println("测试异常情况：分割长度为负数");
        try {
            String str9 = "Test";
            int length9 = -1;
            List<String> result9 = splitStringByLength(str9, length9);
        } catch (IllegalArgumentException e) {
            System.out.println("捕获到预期异常: " + e.getMessage());
        }
        
        System.out.println("\n=== 测试完成 ===");
    }
}
