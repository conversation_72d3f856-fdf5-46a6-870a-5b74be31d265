import com.iciyun.common.utils.HelpMe;
import java.util.List;

/**
 * 测试 HelpMe.splitStringByLength 方法
 */
public class SplitStringTest {
    
    public static void main(String[] args) {
        // 测试用例1：正常分割
        System.out.println("=== 测试用例1：正常分割 ===");
        String str1 = "HelloWorldJavaSpring";
        int length1 = 5;
        List<String> result1 = HelpMe.splitStringByLength(str1, length1);
        System.out.println("原字符串: " + str1);
        System.out.println("分割长度: " + length1);
        System.out.println("分割结果: " + result1);
        System.out.println("分割后的子字符串个数: " + result1.size());
        System.out.println();
        
        // 测试用例2：字符串长度小于分割长度
        System.out.println("=== 测试用例2：字符串长度小于分割长度 ===");
        String str2 = "Java";
        int length2 = 10;
        List<String> result2 = HelpMe.splitStringByLength(str2, length2);
        System.out.println("原字符串: " + str2);
        System.out.println("分割长度: " + length2);
        System.out.println("分割结果: " + result2);
        System.out.println();
        
        // 测试用例3：中文字符串分割
        System.out.println("=== 测试用例3：中文字符串分割 ===");
        String str3 = "这是一个测试中文字符串分割的例子";
        int length3 = 6;
        List<String> result3 = HelpMe.splitStringByLength(str3, length3);
        System.out.println("原字符串: " + str3);
        System.out.println("分割长度: " + length3);
        System.out.println("分割结果: " + result3);
        System.out.println();
        
        // 测试用例4：空字符串
        System.out.println("=== 测试用例4：空字符串 ===");
        String str4 = "";
        int length4 = 3;
        List<String> result4 = HelpMe.splitStringByLength(str4, length4);
        System.out.println("原字符串: \"" + str4 + "\"");
        System.out.println("分割长度: " + length4);
        System.out.println("分割结果: " + result4);
        System.out.println("结果是否为空列表: " + result4.isEmpty());
        System.out.println();
        
        // 测试用例5：null字符串
        System.out.println("=== 测试用例5：null字符串 ===");
        String str5 = null;
        int length5 = 3;
        List<String> result5 = HelpMe.splitStringByLength(str5, length5);
        System.out.println("原字符串: " + str5);
        System.out.println("分割长度: " + length5);
        System.out.println("分割结果: " + result5);
        System.out.println("结果是否为空列表: " + result5.isEmpty());
        System.out.println();
        
        // 测试用例6：分割长度为1
        System.out.println("=== 测试用例6：分割长度为1 ===");
        String str6 = "ABCDE";
        int length6 = 1;
        List<String> result6 = HelpMe.splitStringByLength(str6, length6);
        System.out.println("原字符串: " + str6);
        System.out.println("分割长度: " + length6);
        System.out.println("分割结果: " + result6);
        System.out.println();
        
        // 测试异常情况：分割长度为0或负数
        System.out.println("=== 测试异常情况：分割长度为0 ===");
        try {
            String str7 = "Test";
            int length7 = 0;
            List<String> result7 = HelpMe.splitStringByLength(str7, length7);
        } catch (IllegalArgumentException e) {
            System.out.println("捕获到预期异常: " + e.getMessage());
        }
    }
}
