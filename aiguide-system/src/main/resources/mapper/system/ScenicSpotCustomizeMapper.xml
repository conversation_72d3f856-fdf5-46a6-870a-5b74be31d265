<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--suppress MybatisXMapperXmlInspection -->
<mapper namespace="com.iciyun.system.mapper.ScenicSpotCustomizeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iciyun.system.domain.ScenicSpot">
        <id column="id" property="id" />
        <result column="scenic_spot_id" property="scenicSpotId" />
        <result column="name" property="name" />
        <result column="province_code" property="provinceCode" />
        <result column="province_name" property="provinceName" />
        <result column="city_code" property="cityCode" />
        <result column="city_name" property="cityName" />
        <result column="district_code" property="districtCode" />
        <result column="district_name" property="districtName" />
        <result column="detail_address" property="detailAddress" />
        <result column="kahuna" property="kahuna" />
        <result column="contact_number" property="contactNumber" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="guide_map" property="guideMap" />
        <result column="level" property="level" />
        <result column="latitude" property="latitude" />
        <result column="longitude" property="longitude" />
        <result column="need_token" property="needToken" />
        <result column="label_flag" property="labelFlag" />
        <result column="thumbnail" property="thumbnail" />
        <result column="zoom" property="zoom" />
        <result column="tmap_status" property="tmapStatus" />
        <result column="radius" property="radius" />
        <result column="repetition" property="repetition" />
        <result column="interval_time" property="intervalTime" />
        <result column="service_charge" property="serviceCharge" />
        <result column="tmap_show" property="tmapShow" />
        <result column="identify_type" property="identifyType" />
        <result column="distance" property="distance" />
        <result column="polygon" property="polygon" />
        <result column="poi_id" property="poiId"/>
        <result column="service_activity" property="serviceActivity" />
        <result column="scenic_type" property="scenicType" />
        <result column="cache_ok" property="cacheOk" />
        <result column="audio_cache_ok" property="audioCacheOk" />
        <result column="style_and_language" property="styleAndLanguage" />
        <result column="style_and_language_switch" property="styleAndLanguageSwitch" />
        <result column="label_text" property="labelText" />
    </resultMap>

    <resultMap id="scenicSpotCacheInfoDto" type="com.iciyun.system.domain.dto.ScenicSpotCacheInfoDto">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="cooperation_status" property="cooperationStatus" />
        <result column="status" property="status" />
        <result column="cache_ok" property="cacheOk" />
    </resultMap>

    <select id="selectIdList" parameterType="com.iciyun.system.domain.ScenicSpot">
        select id from scenic_spot
        <where>
            <if test="detailAddress != null  and detailAddress != ''"> and detail_address like concat('%', #{detailAddress}, '%')</if>
            <if test="cacheOk != null"> and cache_ok = #{cacheOk}</if>
            <if test="audioCacheOk != null"> and audio_cache_ok = #{audioCacheOk}</if>
        </where>
    </select>

    <select id="getOneNoCacheOk" resultMap="BaseResultMap">
        select * from scenic_spot where cache_ok = 0 limit 1
    </select>

    <select id="getOneNoAudioCacheOk" resultMap="BaseResultMap">
        select * from scenic_spot where cache_ok = 1 and audio_cache_ok = 0 limit 1
    </select>

    <select id="queryScenicSpot4MiniApp" resultMap="BaseResultMap">
        SELECT
            t.*,
            <choose>
                <when test="targetPosition == 0 ">
                    ST_DistanceSphere(
                        ST_SetSRID(
                            ST_MakePoint(
                                NULLIF(trim(t.latitude), '')::numeric,
                                NULLIF(trim(t.longitude), '')::numeric
                            ),
                            4326
                        ),
                        ST_SetSRID(
                            ST_MakePoint(
                                #{currentLocationX}::numeric,
                                #{currentLocationY}::numeric
                            ),
                            4326
                        )
                    ) AS distance
                </when>
                <when test="targetPosition == 1 ">
                    ST_DistanceSphere(
                        ST_SetSRID(
                            ST_MakePoint(
                                NULLIF(trim(t.latitude), '')::numeric,
                                NULLIF(trim(t.longitude), '')::numeric
                            ),
                            4326
                        ),
                        ST_SetSRID(
                            ST_MakePoint(
                                #{currentCityX}::numeric,
                                #{currentCityY}::numeric
                            ),
                            4326
                        )
                    ) AS distance
                </when>
            </choose>
            FROM
                scenic_spot t
            <where>
                status = '0'
                <if test="name != null and name != ''">
                    and (t."name" like  concat('%', #{name}, '%') or t.detail_address like  concat('%', #{name}, '%') or similarity(t."name", #{name}) > 0.5)
                </if>
                <if test="cityCode != null and cityCode != ''">
                    and t.city_code = #{cityCode}
                </if>
            </where>
            ORDER BY distance

    </select>

    <update id="upServiceActivityNull">
        UPDATE scenic_spot SET service_activity = NULL WHERE id = #{id}
    </update>

    <update id="upNeedTokenNull">
        UPDATE scenic_spot SET need_token = NULL WHERE id = #{id}
    </update>

    <update id="updateCacheOk">
        UPDATE scenic_spot SET cache_ok = #{cacheOk} WHERE id = #{id}
    </update>

    <update id="updateLabelText">
        UPDATE scenic_spot SET label_text = #{labelText} WHERE id = #{id}
    </update>

    <update id="updateAudioCacheOk">
        UPDATE scenic_spot SET audio_cache_ok = #{audioCacheOk} WHERE id = #{id}
    </update>

    <update id="resetCache" parameterType="com.iciyun.system.domain.ScenicSpot">
        update scenic_spot set cache_ok = #{cacheOk},audio_cache_ok = #{audioCacheOk},label_text = #{labelText} where id = #{id}
    </update>

    <select id="getOneByCacheOk" resultMap="BaseResultMap">
        select * from scenic_spot where cache_ok = #{cacheOk} and status = 'ON' limit 1
    </select>

    <select id="scenicSpotCacheInfo_One" resultMap="scenicSpotCacheInfoDto">
        SELECT
            s.ID,
            s.NAME,
            s.status,
            s.cache_ok,
            CASE

                WHEN o.scenic_id IS NOT NULL THEN
                    1 ELSE 0
                END AS cooperation_status
        FROM
            scenic_spot s
                LEFT JOIN secenic_channel_operate o ON s.ID = o.scenic_id
        WHERE
            s.ID = #{id}
        GROUP BY
            s.ID,
            s.NAME,
            s.status,
            s.cache_ok,
            o.scenic_id
    </select>

    <select id="scenicSpotCacheInfo" resultMap="scenicSpotCacheInfoDto">
        SELECT
            s.id,
            s.name,
            s.status,
            s.cache_ok,
            CASE
                WHEN o.scenic_id IS NOT NULL THEN 1
                ELSE 0
                END AS cooperation_status
        FROM
            scenic_spot s
                LEFT JOIN
            secenic_channel_operate o ON s.id = o.scenic_id
        GROUP BY
            s.id, s.name,  s.status,s.cache_ok, o.scenic_id
    </select>

    <select id="unCacheLabelCount">
        SELECT COUNT
                   ( 1 )
        FROM
            sys_tourist_label
        WHERE
                tourist_id IN (
                SELECT ID
                FROM
                    (
                        SELECT
                            s.ID,
                            s.NAME,
                            s.status,
                            s.cache_ok,
                            CASE

                                WHEN o.scenic_id IS NOT NULL THEN
                                    1 ELSE 0
                                END AS cooperation_status
                        FROM
                            scenic_spot s
                                LEFT JOIN secenic_channel_operate o ON s.ID = o.scenic_id
                        GROUP BY
                            s.ID,
                            s.NAME,
                            s.status,
                            s.cache_ok,
                            o.scenic_id
                    )
                WHERE
                    cache_ok = 0
                  AND status = 'ON'
            )
    </select>

    <select id="nonLabels" resultMap="BaseResultMap">
        SELECT * from scenic_spot t
        where t.id not in (
        select tt.tourist_id from sys_tourist_label tt
        )
        limit #{limitQuery}
    </select>

</mapper>
