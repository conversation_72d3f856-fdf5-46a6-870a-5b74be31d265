import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * 音频时长计算使用示例
 * 展示如何在实际项目中使用 HelpMe 的音频时长计算方法
 */
public class AudioDurationExample {
    
    public static void main(String[] args) {
        System.out.println("=== 音频时长计算使用示例 ===\n");
        
        // 示例1：通过URL计算音频时长
        example1_CalculateFromUrl();
        
        // 示例2：通过文件大小和参数计算时长
        example2_CalculateFromSize();
        
        // 示例3：在TTS服务中的应用场景
        example3_TTSScenario();
        
        // 示例4：批量处理音频文件
        example4_BatchProcessing();
    }
    
    /**
     * 示例1：通过URL计算音频时长
     */
    private static void example1_CalculateFromUrl() {
        System.out.println("示例1：通过URL计算音频时长");
        System.out.println("适用场景：音频文件存储在OSS、CDN等云存储服务中");
        System.out.println();
        
        String audioUrl = "https://ai-guide.tos-cn-beijing.volces.com/cache/mp3/guide/1752030857909/chat_1.0.wav";
        
        System.out.println("使用方法：");
        System.out.println("String audioUrl = \"" + audioUrl + "\";");
        System.out.println("double duration = HelpMe.calculateWavDurationFromUrl(audioUrl);");
        System.out.println("String formattedDuration = HelpMe.formatDuration(duration);");
        System.out.println();
        
        System.out.println("实际应用代码示例：");
        System.out.println("@Service");
        System.out.println("public class AudioService {");
        System.out.println("    public AudioInfo getAudioInfo(String audioUrl) {");
        System.out.println("        double duration = HelpMe.calculateWavDurationFromUrl(audioUrl);");
        System.out.println("        if (duration > 0) {");
        System.out.println("            return new AudioInfo(audioUrl, duration, HelpMe.formatDuration(duration));");
        System.out.println("        } else {");
        System.out.println("            throw new RuntimeException(\"无法获取音频时长\");");
        System.out.println("        }");
        System.out.println("    }");
        System.out.println("}");
        System.out.println();
    }
    
    /**
     * 示例2：通过文件大小和参数计算时长
     */
    private static void example2_CalculateFromSize() {
        System.out.println("示例2：通过文件大小和参数计算时长");
        System.out.println("适用场景：已知音频文件的技术参数，快速计算时长");
        System.out.println();
        
        System.out.println("使用方法：");
        System.out.println("// 假设有一个1MB的WAV文件，CD质量");
        System.out.println("long fileSize = 1024 * 1024; // 1MB");
        System.out.println("int sampleRate = 44100;      // 44.1kHz");
        System.out.println("short channels = 2;          // 立体声");
        System.out.println("short bitsPerSample = 16;    // 16位");
        System.out.println();
        System.out.println("double duration = HelpMe.calculateWavDurationFromSize(fileSize, sampleRate, channels, bitsPerSample);");
        System.out.println("System.out.println(\"播放时长: \" + HelpMe.formatDuration(duration));");
        System.out.println();
        
        // 实际计算示例
        long fileSize = 1024 * 1024; // 1MB
        int sampleRate = 44100;
        short channels = 2;
        short bitsPerSample = 16;
        
        double duration = calculateWavDurationFromSize(fileSize, sampleRate, channels, bitsPerSample);
        System.out.println("计算结果：1MB的CD质量WAV文件播放时长约为 " + formatDuration(duration));
        System.out.println();
    }
    
    /**
     * 示例3：在TTS服务中的应用场景
     */
    private static void example3_TTSScenario() {
        System.out.println("示例3：在TTS服务中的应用场景");
        System.out.println("结合文本分割和音频合并功能，计算总播放时长");
        System.out.println();
        
        System.out.println("应用场景代码示例：");
        System.out.println("@Service");
        System.out.println("public class TTSService {");
        System.out.println("    ");
        System.out.println("    public TTSResult generateAudio(String longText) {");
        System.out.println("        // 1. 分割长文本");
        System.out.println("        List<String> textSegments = HelpMe.splitStringByLength(longText, 1000);");
        System.out.println("        ");
        System.out.println("        // 2. 生成音频片段并计算时长");
        System.out.println("        List<File> audioFiles = new ArrayList<>();");
        System.out.println("        double totalDuration = 0;");
        System.out.println("        ");
        System.out.println("        for (String segment : textSegments) {");
        System.out.println("            File audioFile = ttsEngine.synthesize(segment);");
        System.out.println("            audioFiles.add(audioFile);");
        System.out.println("            ");
        System.out.println("            // 计算每个片段的时长");
        System.out.println("            double segmentDuration = HelpMe.getWavDurationFromFile(audioFile);");
        System.out.println("            totalDuration += segmentDuration;");
        System.out.println("        }");
        System.out.println("        ");
        System.out.println("        // 3. 合并音频文件");
        System.out.println("        File mergedFile = HelpMe.mergeWavFiles(audioFiles);");
        System.out.println("        ");
        System.out.println("        // 4. 验证合并后的时长");
        System.out.println("        double actualDuration = HelpMe.getWavDurationFromFile(mergedFile);");
        System.out.println("        ");
        System.out.println("        return new TTSResult(mergedFile, actualDuration, HelpMe.formatDuration(actualDuration));");
        System.out.println("    }");
        System.out.println("}");
        System.out.println();
    }
    
    /**
     * 示例4：批量处理音频文件
     */
    private static void example4_BatchProcessing() {
        System.out.println("示例4：批量处理音频文件");
        System.out.println("计算多个音频文件的总时长");
        System.out.println();
        
        System.out.println("使用方法：");
        System.out.println("List<File> audioFiles = Arrays.asList(");
        System.out.println("    new File(\"audio1.wav\"),");
        System.out.println("    new File(\"audio2.wav\"),");
        System.out.println("    new File(\"audio3.wav\")");
        System.out.println(");");
        System.out.println();
        System.out.println("double totalDuration = HelpMe.calculateTotalDuration(audioFiles);");
        System.out.println("System.out.println(\"总播放时长: \" + HelpMe.formatDuration(totalDuration));");
        System.out.println();
        
        System.out.println("实际应用场景：");
        System.out.println("@RestController");
        System.out.println("public class AudioController {");
        System.out.println("    ");
        System.out.println("    @GetMapping(\"/audio/duration\")");
        System.out.println("    public ResponseEntity<AudioDurationResponse> getAudioDuration(@RequestParam String audioUrl) {");
        System.out.println("        try {");
        System.out.println("            double duration = HelpMe.calculateWavDurationFromUrl(audioUrl);");
        System.out.println("            ");
        System.out.println("            if (duration > 0) {");
        System.out.println("                AudioDurationResponse response = new AudioDurationResponse();");
        System.out.println("                response.setUrl(audioUrl);");
        System.out.println("                response.setDurationSeconds(duration);");
        System.out.println("                response.setFormattedDuration(HelpMe.formatDuration(duration));");
        System.out.println("                response.setSuccess(true);");
        System.out.println("                ");
        System.out.println("                return ResponseEntity.ok(response);");
        System.out.println("            } else {");
        System.out.println("                return ResponseEntity.badRequest().build();");
        System.out.println("            }");
        System.out.println("        } catch (Exception e) {");
        System.out.println("            return ResponseEntity.status(500).build();");
        System.out.println("        }");
        System.out.println("    }");
        System.out.println("}");
        System.out.println();
    }
    
    // 简化的辅助方法（实际使用时调用 HelpMe 中的方法）
    private static double calculateWavDurationFromSize(long fileSize, int sampleRate, short channels, short bitsPerSample) {
        if (fileSize <= 44 || sampleRate <= 0 || channels <= 0 || bitsPerSample <= 0) {
            return -1;
        }
        double duration = (double) (fileSize - 44) * 8 / (sampleRate * bitsPerSample * channels);
        return Math.round(duration * 100.0) / 100.0;
    }
    
    private static String formatDuration(double durationSeconds) {
        if (durationSeconds < 0) {
            return "未知时长";
        }
        int totalSeconds = (int) Math.round(durationSeconds);
        int minutes = totalSeconds / 60;
        int seconds = totalSeconds % 60;
        if (minutes > 0) {
            return minutes + "分" + seconds + "秒";
        } else {
            return seconds + "秒";
        }
    }
}
