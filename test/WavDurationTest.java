import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

/**
 * WAV音频时长计算功能测试
 */
public class WavDurationTest {
    
    /**
     * 通过 WAV 音频文件 URL 计算播放时长
     */
    public static double calculateWavDurationFromUrl(String url) {
        if (isNull(url)) {
            return -1;
        }
        
        try {
            // 使用 HEAD 请求获取文件大小和基本信息
            URL audioUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) audioUrl.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(10000);    // 10秒读取超时
            
            int responseCode = connection.getResponseCode();
            if (responseCode != 200) {
                System.err.println("无法访问音频文件，HTTP状态码: " + responseCode);
                return -1;
            }
            
            // 获取文件大小
            long fileSize = connection.getContentLengthLong();
            if (fileSize <= 44) { // WAV文件至少需要44字节的头部
                System.err.println("文件大小异常: " + fileSize + " 字节");
                return -1;
            }
            
            connection.disconnect();
            
            // 下载文件头部信息（前44字节）来获取音频参数
            connection = (HttpURLConnection) audioUrl.openConnection();
            connection.setRequestProperty("Range", "bytes=0-43"); // 只下载前44字节
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            
            if (connection.getResponseCode() != 206 && connection.getResponseCode() != 200) {
                // 如果不支持范围请求，使用默认参数估算
                return calculateWavDurationFromSize(fileSize, 44100, (short) 1, (short) 16);
            }
            
            try (InputStream is = connection.getInputStream()) {
                byte[] header = new byte[44];
                int bytesRead = is.read(header);
                
                if (bytesRead < 44) {
                    // 使用默认参数估算
                    return calculateWavDurationFromSize(fileSize, 44100, (short) 1, (short) 16);
                }
                
                // 验证WAV文件格式
                String riff = new String(header, 0, 4);
                String wave = new String(header, 8, 4);
                if (!"RIFF".equals(riff) || !"WAVE".equals(wave)) {
                    System.err.println("不是有效的WAV文件格式");
                    return -1;
                }
                
                // 解析音频参数
                short channels = (short) (((header[23] & 0xFF) << 8) | (header[22] & 0xFF));
                int sampleRate = ((header[27] & 0xFF) << 24) | ((header[26] & 0xFF) << 16) | 
                               ((header[25] & 0xFF) << 8) | (header[24] & 0xFF);
                short bitsPerSample = (short) (((header[35] & 0xFF) << 8) | (header[34] & 0xFF));
                
                return calculateWavDurationFromSize(fileSize, sampleRate, channels, bitsPerSample);
            }
            
        } catch (Exception e) {
            System.err.println("计算音频时长失败: " + e.getMessage());
            return -1;
        }
    }

    /**
     * 通过文件大小和音频参数计算 WAV 播放时长
     */
    public static double calculateWavDurationFromSize(long fileSize, int sampleRate, short channels, short bitsPerSample) {
        if (fileSize <= 44 || sampleRate <= 0 || channels <= 0 || bitsPerSample <= 0) {
            return -1;
        }
        
        // Length = (Size - 44) * 8 / (Rate * Precision * Channels)
        double duration = (double) (fileSize - 44) * 8 / (sampleRate * bitsPerSample * channels);
        
        return Math.round(duration * 100.0) / 100.0; // 保留两位小数
    }

    /**
     * 格式化播放时长为可读字符串
     */
    public static String formatDuration(double durationSeconds) {
        if (durationSeconds < 0) {
            return "未知时长";
        }
        
        int totalSeconds = (int) Math.round(durationSeconds);
        int minutes = totalSeconds / 60;
        int seconds = totalSeconds % 60;
        
        if (minutes > 0) {
            return minutes + "分" + seconds + "秒";
        } else {
            return seconds + "秒";
        }
    }

    // 辅助方法
    public static boolean isNull(String str) {
        return str == null || str.trim().isEmpty();
    }

    public static void main(String[] args) {
        System.out.println("=== WAV音频时长计算功能测试 ===\n");
        
        // 测试1：通过文件大小和参数计算时长
        testCalculateFromSize();
        
        // 测试2：通过URL计算时长（需要网络连接）
        testCalculateFromUrl();
        
        // 测试3：测试不同音频格式参数
        testDifferentFormats();
        
        // 测试4：测试格式化功能
        testFormatDuration();
    }
    
    private static void testCalculateFromSize() {
        System.out.println("=== 测试1：通过文件大小计算时长 ===");
        
        // 测试用例1：标准CD质量音频 (44.1kHz, 16bit, 立体声)
        long fileSize1 = 1764044; // 约10秒的音频文件大小
        double duration1 = calculateWavDurationFromSize(fileSize1, 44100, (short) 2, (short) 16);
        System.out.println("CD质量音频 (44.1kHz, 16bit, 立体声):");
        System.out.println("  文件大小: " + fileSize1 + " 字节");
        System.out.println("  计算时长: " + duration1 + " 秒");
        System.out.println("  格式化时长: " + formatDuration(duration1));
        System.out.println();
        
        // 测试用例2：高质量音频 (48kHz, 24bit, 立体声)
        long fileSize2 = 2880044; // 约10秒的音频文件大小
        double duration2 = calculateWavDurationFromSize(fileSize2, 48000, (short) 2, (short) 24);
        System.out.println("高质量音频 (48kHz, 24bit, 立体声):");
        System.out.println("  文件大小: " + fileSize2 + " 字节");
        System.out.println("  计算时长: " + duration2 + " 秒");
        System.out.println("  格式化时长: " + formatDuration(duration2));
        System.out.println();
        
        // 测试用例3：单声道音频 (22.05kHz, 16bit, 单声道)
        long fileSize3 = 441044; // 约10秒的音频文件大小
        double duration3 = calculateWavDurationFromSize(fileSize3, 22050, (short) 1, (short) 16);
        System.out.println("单声道音频 (22.05kHz, 16bit, 单声道):");
        System.out.println("  文件大小: " + fileSize3 + " 字节");
        System.out.println("  计算时长: " + duration3 + " 秒");
        System.out.println("  格式化时长: " + formatDuration(duration3));
        System.out.println();
    }
    
    private static void testCalculateFromUrl() {
        System.out.println("=== 测试2：通过URL计算时长 ===");
        
        String testUrl = "https://ai-guide.tos-cn-beijing.volces.com/cache/mp3/guide/1752030857909/chat_1.0.wav";
        
        System.out.println("测试URL: " + testUrl);
        System.out.println("正在计算时长...");
        
        long startTime = System.currentTimeMillis();
        double duration = calculateWavDurationFromUrl(testUrl);
        long endTime = System.currentTimeMillis();
        
        if (duration > 0) {
            System.out.println("计算成功!");
            System.out.println("  播放时长: " + duration + " 秒");
            System.out.println("  格式化时长: " + formatDuration(duration));
            System.out.println("  计算耗时: " + (endTime - startTime) + "ms");
        } else {
            System.out.println("计算失败，可能的原因:");
            System.out.println("  1. 网络连接问题");
            System.out.println("  2. URL无效或文件不存在");
            System.out.println("  3. 不是有效的WAV文件");
        }
        System.out.println();
    }
    
    private static void testDifferentFormats() {
        System.out.println("=== 测试3：不同音频格式参数 ===");
        
        long baseFileSize = 1000000; // 1MB文件大小
        
        System.out.println("相同文件大小 (" + baseFileSize + " 字节) 不同格式的时长:");
        
        // 不同采样率
        double duration1 = calculateWavDurationFromSize(baseFileSize, 8000, (short) 1, (short) 16);
        double duration2 = calculateWavDurationFromSize(baseFileSize, 16000, (short) 1, (short) 16);
        double duration3 = calculateWavDurationFromSize(baseFileSize, 44100, (short) 1, (short) 16);
        
        System.out.println("  8kHz, 16bit, 单声道:  " + formatDuration(duration1));
        System.out.println("  16kHz, 16bit, 单声道: " + formatDuration(duration2));
        System.out.println("  44.1kHz, 16bit, 单声道: " + formatDuration(duration3));
        System.out.println();
    }
    
    private static void testFormatDuration() {
        System.out.println("=== 测试4：时长格式化功能 ===");
        
        double[] testDurations = {5.5, 30.0, 65.3, 125.8, 3661.2, -1.0};
        
        for (double duration : testDurations) {
            System.out.println(duration + " 秒 -> " + formatDuration(duration));
        }
        System.out.println();
    }
}
